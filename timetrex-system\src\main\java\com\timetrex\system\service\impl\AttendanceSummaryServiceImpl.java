package com.timetrex.system.service.impl;

import java.time.LocalDate;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.timetrex.system.mapper.AttendanceSummaryMapper;
import com.timetrex.system.domain.AttendanceSummary;
import com.timetrex.system.service.IAttendanceSummaryService;
import com.timetrex.common.core.text.Convert;

/**
 * 考勤概况统计与打卡明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Service
public class AttendanceSummaryServiceImpl implements IAttendanceSummaryService
{
    @Autowired
    private AttendanceSummaryMapper attendanceSummaryMapper;

    /**
     * 查询考勤概况统计与打卡明细
     *
     * @param id 考勤概况统计与打卡明细主键
     * @return 考勤概况统计与打卡明细
     */
    @Override
    public AttendanceSummary selectAttendanceSummaryById(Long id)
    {
        return attendanceSummaryMapper.selectAttendanceSummaryById(id);
    }

    /**
     * 查询考勤概况统计与打卡明细列表
     *
     * @param attendanceSummary 考勤概况统计与打卡明细
     * @return 考勤概况统计与打卡明细
     */
    @Override
    public List<AttendanceSummary> selectAttendanceSummaryList(AttendanceSummary attendanceSummary)
    {
        return attendanceSummaryMapper.selectAttendanceSummaryList(attendanceSummary);
    }

    /**
     * 根据月份和员工编号查询考勤概况
     *
     * @param month 月份
     * @param staffNo 员工编号
     * @return 考勤概况统计集合
     */
    @Override
    public List<AttendanceSummary> selectAttendanceSummaryByMonthAndStaffNo(LocalDate month, String staffNo)
    {
        return attendanceSummaryMapper.selectAttendanceSummaryByMonthAndStaffNo(month, staffNo);
    }

    /**
     * 根据月份查询考勤概况
     *
     * @param month 月份
     * @return 考勤概况统计集合
     */
    @Override
    public List<AttendanceSummary> selectAttendanceSummaryByMonth(LocalDate month)
    {
        return attendanceSummaryMapper.selectAttendanceSummaryByMonth(month);
    }

    /**
     * 新增考勤概况统计与打卡明细
     *
     * @param attendanceSummary 考勤概况统计与打卡明细
     * @return 结果
     */
    @Override
    public int insertAttendanceSummary(AttendanceSummary attendanceSummary)
    {
        return attendanceSummaryMapper.insertAttendanceSummary(attendanceSummary);
    }

    /**
     * 批量新增考勤概况
     *
     * @param attendanceSummaryList 考勤概况列表
     * @return 结果
     */
    @Override
    public int batchInsertAttendanceSummary(List<AttendanceSummary> attendanceSummaryList)
    {
        return attendanceSummaryMapper.batchInsertAttendanceSummary(attendanceSummaryList);
    }

    /**
     * 修改考勤概况统计与打卡明细
     *
     * @param attendanceSummary 考勤概况统计与打卡明细
     * @return 结果
     */
    @Override
    public int updateAttendanceSummary(AttendanceSummary attendanceSummary)
    {
        return attendanceSummaryMapper.updateAttendanceSummary(attendanceSummary);
    }

    /**
     * 批量删除考勤概况统计与打卡明细
     *
     * @param ids 需要删除的考勤概况统计与打卡明细主键
     * @return 结果
     */
    @Override
    public int deleteAttendanceSummaryByIds(String ids)
    {
        return attendanceSummaryMapper.deleteAttendanceSummaryByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除考勤概况统计与打卡明细信息
     *
     * @param id 考勤概况统计与打卡明细主键
     * @return 结果
     */
    @Override
    public int deleteAttendanceSummaryById(Long id)
    {
        return attendanceSummaryMapper.deleteAttendanceSummaryById(id);
    }

    /**
     * 批量保存考勤概况统计
     *
     * @param attendanceSummaryList 考勤概况统计列表
     * @return 结果
     */
    @Override
    public int batchSave(List<AttendanceSummary> attendanceSummaryList)
    {
        if (attendanceSummaryList == null || attendanceSummaryList.isEmpty()) {
            return 0;
        }
        return batchInsertAttendanceSummary(attendanceSummaryList);
    }
}
