# 企微API数据结构优化说明

## 优化背景

根据企微官方API文档，对返回数据的处理逻辑进行了重大优化，确保正确解析企微API返回的数据结构。

## 1. 打卡日报数据优化

### 原始数据结构
企微API返回的打卡日报数据结构如下：
```json
{
    "errcode": 0,
    "errmsg": "ok",
    "datas": [
        {
            "base_info": {
                "date": 1599062400,
                "name": "张三",
                "acctid": "ZhangSan",
                "departs_name": "有家企业/部门A4",
                "rule_info": {
                    "groupname": "规则测试",
                    "schedulename": "班次名称"
                }
            },
            "summary_info": {
                "checkin_count": 2,
                "regular_work_sec": 31,
                "standard_work_sec": 120,
                "earliest_time": 38827,
                "lastest_time": 38858
            }
        }
    ]
}
```

### 优化后的字段映射

| 系统字段 | 企微API字段路径 | 说明 |
|---------|----------------|------|
| staffNo | datas[].base_info.acctid | 用户ID |
| name | datas[].base_info.name | 用户姓名 |
| department | datas[].base_info.departs_name | 部门名称 |
| recordDate | datas[].base_info.date | 日期（时间戳转换） |
| workRule | datas[].base_info.rule_info.groupname | 打卡规则名 |
| shift | datas[].base_info.rule_info.schedulename | 班次名称 |
| checkCount | datas[].summary_info.checkin_count | 打卡次数 |
| standardHours | datas[].summary_info.standard_work_sec | 标准工作时长（秒转小时） |
| actualHours | datas[].summary_info.regular_work_sec | 实际工作时长（秒转小时） |
| earliestCheck | datas[].summary_info.earliest_time | 最早打卡时间（时间戳转换） |
| latestCheck | datas[].summary_info.lastest_time | 最晚打卡时间（时间戳转换） |

### 关键优化点

1. **嵌套结构解析**：正确处理`base_info`和`summary_info`嵌套对象
2. **时间戳转换**：将Unix时间戳转换为LocalDate和LocalTime
3. **字段名修正**：注意企微API中是`lastest_time`而不是`latest_time`
4. **单位转换**：工作时长从秒转换为小时（除以3600）

## 2. 打卡详情数据优化

### 原始数据结构
企微API返回的打卡详情数据结构如下：
```json
{
    "errcode": 0,
    "errmsg": "ok",
    "checkindata": [
        {
            "userid": "james",
            "groupname": "打卡一组",
            "checkin_type": "上班打卡",
            "exception_type": "地点异常",
            "checkin_time": 1492617610,
            "location_title": "依澜府",
            "notes": "路上堵车，迟到了5分钟",
            "deviceid": "E5FA89F6-3926-4972-BE4F-4A7ACF4701E2",
            "sch_checkin_time": 1492617610
        }
    ]
}
```

### 优化后的字段映射

| 系统字段 | 企微API字段 | 说明 |
|---------|------------|------|
| staffNo | userid | 用户ID |
| name | userid | 姓名（如果API不返回name则使用userid） |
| department | - | 默认"未知部门"（需其他API补充） |
| position | - | 默认"员工"（需其他API补充） |
| checkDate | checkin_time | 打卡日期（时间戳转换） |
| actualTime | checkin_time | 打卡时间（时间戳转换） |
| checkType | checkin_type | 打卡类型（上班/下班/外出） |
| checkStatus | exception_type | 打卡状态（正常/异常） |
| location | location_title | 打卡地点 |
| deviceId | deviceid | 设备ID |
| remark | notes | 备注信息 |
| workRule | groupname | 工作规则 |
| scheduledTime | sch_checkin_time | 应打卡时间（时间戳转换） |

### 关键优化点

1. **平铺结构**：打卡详情是平铺结构，直接取字段值
2. **类型判断**：根据`checkin_type`中文描述判断打卡类型
3. **状态处理**：根据`exception_type`判断是否异常
4. **缺失字段处理**：部分字段可能需要其他API补充

## 3. 数据转换优化

### 时间戳转换
```java
// 企微时间戳转LocalDate
Long timestamp = getLongValue(data, "date");
LocalDate date = Instant.ofEpochSecond(timestamp)
    .atZone(ZoneId.of("Asia/Shanghai"))
    .toLocalDate();

// 企微时间戳转LocalTime
Long timestamp = getLongValue(data, "checkin_time");
LocalTime time = Instant.ofEpochSecond(timestamp)
    .atZone(ZoneId.of("Asia/Shanghai"))
    .toLocalTime();
```

### 工作时长转换
```java
// 秒转小时，保留2位小数
BigDecimal hours = getBigDecimalValue(data, "regular_work_sec", 3600);
```

### 异常处理
```java
// 每个数据项都有独立的try-catch
// 单个数据转换失败不影响其他数据
try {
    // 数据转换逻辑
} catch (Exception e) {
    log.error("转换数据失败: {}", data, e);
}
```

## 4. 性能优化

### 分批处理
- 数据获取：按用户分批调用API
- 数据保存：按配置的批次大小分批入库
- 内存控制：避免大量数据同时加载

### 错误恢复
- API调用失败：记录错误继续处理其他批次
- 数据转换失败：跳过错误数据继续处理
- 数据库操作失败：事务回滚保证数据一致性

## 5. 配置优化

### 用户ID配置
```yaml
wechat:
  sync:
    userIds:
      - user001
      - user002
    batchSize: 10
    dbBatchSize: 500
```

### 性能参数
- `batchSize`: API调用批次大小
- `dbBatchSize`: 数据库批量插入大小
- `requestInterval`: API请求间隔

## 6. 日志监控

### 详细日志
- API调用结果
- 数据转换进度
- 批次处理状态
- 错误详情记录

### 性能监控
- 处理时间统计
- 数据量统计
- 成功率监控

## 7. 注意事项

1. **字段名称**：严格按照企微API文档的字段名
2. **数据结构**：注意嵌套对象和平铺结构的区别
3. **时区处理**：统一使用Asia/Shanghai时区
4. **异常处理**：确保单个数据错误不影响整体处理
5. **性能调优**：根据实际情况调整批次大小
