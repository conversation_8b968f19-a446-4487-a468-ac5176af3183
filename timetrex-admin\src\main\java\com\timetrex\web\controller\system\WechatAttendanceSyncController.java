package com.timetrex.web.controller.system;

import com.timetrex.common.annotation.Log;
import com.timetrex.common.core.controller.BaseController;
import com.timetrex.common.core.domain.AjaxResult;
import com.timetrex.common.enums.BusinessType;
import com.timetrex.system.service.IWechatAttendanceSyncService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

/**
 * 企业微信打卡数据同步控制器
 */
@Controller
@RequestMapping("/system/attendance/wechat")
public class WechatAttendanceSyncController extends BaseController {
    
    private String prefix = "system/attendance/wechat";
    
    @Autowired
    private IWechatAttendanceSyncService wechatAttendanceSyncService;
    
    @RequiresPermissions("system:attendance:wechat:view")
    @GetMapping()
    public String wechat() {
        return prefix + "/sync";
    }
    
    /**
     * 同步企业微信打卡数据
     */
    @RequiresPermissions("system:attendance:wechat:sync")
    @Log(title = "企业微信打卡数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/sync")
    @ResponseBody
    public AjaxResult sync(String yearMonth) {
        if (yearMonth == null || yearMonth.isEmpty()) {
            return AjaxResult.error("请选择要同步的年月");
        }
        
        Map<String, Object> result = wechatAttendanceSyncService.syncMonthlyAttendance(yearMonth);
        
        if ((boolean) result.get("success")) {
            return AjaxResult.success("同步成功，共同步概况数据 " + result.get("summaryCount") + " 条，详情数据 " + result.get("detailCount") + " 条");
        } else {
            return AjaxResult.error(String.join("<br/>", (Iterable<String>) result.get("errorMessages")));
        }
    }
}