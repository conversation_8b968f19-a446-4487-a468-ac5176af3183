<!DOCTYPE html>
<html lang="zh">
<head>
	<th:block th:include="include :: header('表格搜索')" />
	<th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
    <div class="container-div">
		<div class="row">
			<div class="col-sm-12 search-collapse">
			    <p class="select-title">普通条件查询</p>
				<form id="ordinary-form">
					<div class="select-list">
						<ul>
							<li>
								商户编号：<input type="text" name="userId"/>
							</li>
							<li>
								终端编号：<input type="text" name="termId"/>
							</li>
							<li>
								处理状态：<select name="status">
									<option value="">所有</option>
									<option value="0">初始</option>
									<option value="1">处理中</option>
									<option value="2">交易成功</option>
									<option value="3">交易失败</option>
								</select>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
							    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>
						</ul>
					</div>
				</form>
			</div>

			<div class="col-sm-12 search-collapse">
			    <p class="select-title">时间条件查询</p>
				<form id="time-form">
					<div class="select-list">
						<ul>
							<li>
								商户编号：<input type="text" name="userId"/>
							</li>
							<li>
								终端编号：<input type="text" name="termId"/>
							</li>
							<li class="select-time">
								<label>创建时间： </label>
								<input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginTime]"/>
								<span>-</span>
								<input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endTime]"/>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
							    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('time-form')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>
						</ul>
					</div>
				</form>
			</div>

			<div class="col-sm-12 search-collapse">
			    <p class="select-title">多级联动下拉查询</p>
				<form id="cxselect-form">
					<div class="select-list">
						<ul id="element">
						    <li>
								商户编号：<input type="text" name="userId"/>
							</li>
							<li>
								充值类型：<select class="type"></select>
							</li>
							<li>
								充值路由：<select class="router"></select>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
							    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('cxselect-form')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>
						</ul>
					</div>
				</form>
			</div>

			<div class="col-sm-12 search-collapse">
			    <p class="select-title">下拉多选条件查询</p>
				<form id="select-form">
					<div class="select-list">
						<ul>
							<li>
								商户编号：<input type="text" name="userId"/>
							</li>
							<li>
								终端编号：<input type="text" name="termId"/>
							</li>
							<li class="select-selectpicker">
								<label>操作类型： </label><select class="selectpicker" data-none-selected-text="请选择" multiple>
									<option value="">所有</option>
									<option value="0">初始</option>
									<option value="1">处理中</option>
									<option value="2">交易成功</option>
									<option value="3">交易失败</option>
								</select>
							</li>
							<li>
								<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
							    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('select-form')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>
						</ul>
					</div>
				</form>
			</div>

			<div class="col-sm-12 search-collapse">
			    <p class="select-title">复杂条件查询</p>
				<form id="complex-form">
					<div class="select-list">
						<ul>
							<li>
								<label style="width: 80px">商户编号：</label>
								<input type="text" name="userId"/>
							</li>
							<li>
								<label style="width: 80px">订单号：</label>
								<input type="text" name="orderNo"/>
							</li>
							<li>
								<label style="width: 80px">日期：</label>
								<input type="text" class="time-input" placeholder="日期"/>
							</li>
							<li class="select-selectpicker">
								<label style="width: 80px">状态：</label>
								<select class="selectpicker" data-none-selected-text="请选择" multiple>
									<option value="">所有</option>
									<option value="0">初始</option>
									<option value="1">处理中</option>
									<option value="2">交易成功</option>
									<option value="3">交易失败</option>
								</select>
							</li>
							<li>
								<label style="width: 80px">供货商通道：</label>
								<select>
									<option value="">所有</option>
									<option value="0">腾讯</option>
									<option value="1">天猫</option>
									<option value="2">京东</option>
								</select>
							</li>
							<li>
								<label style="width: 80px">来源：</label>
								<select>
									<option value="">所有</option>
									<option value="0">手机</option>
									<option value="1">电脑</option>
									<option value="2">第三方</option>
								</select>
							</li>
							<li>
								<label style="width: 80px">运营商：</label>
								<select>
									<option value="">所有</option>
									<option value="0">移动</option>
									<option value="1">电信</option>
									<option value="2">联通</option>
								</select>
							</li>
							<li class="select-time">
								<label style="width: 80px">回调时间：</label>
								<input type="text" class="time-input" placeholder="开始时间"/>
								<span>-</span>
								<input type="text" class="time-input" placeholder="结束时间"/>
							</li>

							<li>
								<a class="btn btn-primary btn-rounded btn-sm m50" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
							    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('complex-form')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
							</li>
						</ul>
					</div>
				</form>
			</div>
		</div>
	</div>
	<th:block th:include="include :: footer" />
	<th:block th:include="include :: bootstrap-select-js" />
	<th:block th:include="include :: jquery-cxselect-js" />

	<script th:inline="javascript">
	    // 直接返回获取
   		var data = [{"v":"taobao","n":"淘宝","s":[{"v":"tm","n":"天猫"},{"v":"jhs","n":"聚划算"}]},{"v":"jd","n":"京东","s":[{"v":"jdcs","n":"京东超市"},{"v":"jdsx","n":"京东生鲜"}]}];
    	$('#element').cxSelect({
    	  selects: ['type', 'router'],
    	  jsonValue: 'v',
    	  data: data
    	});
    </script>
</body>
</html>
