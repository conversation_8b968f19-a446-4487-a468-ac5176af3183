<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增厂商配置')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-vendor-add">
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">厂商名称：</label>
                    <div class="col-sm-8">
                        <input name="name" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <div class="form-group">
                    <label class="col-sm-3 control-label is-required">平均计薪工作日：</label>
                    <div class="col-sm-8">
                        <input name="avgWorkDays" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "system/vendor"
        $("#form-vendor-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-vendor-add').serialize());
            }
        }
    </script>
</body>
</html>