package com.timetrex.web.controller.system;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.timetrex.common.annotation.Log;
import com.timetrex.common.core.controller.BaseController;
import com.timetrex.common.core.domain.AjaxResult;
import com.timetrex.common.core.page.TableDataInfo;
import com.timetrex.common.enums.BusinessType;
import com.timetrex.system.domain.AttendanceDetail;
import com.timetrex.system.domain.AttendanceSummary;
import com.timetrex.system.service.IAttendanceDetailService;
import com.timetrex.system.service.IAttendanceSummaryService;
import com.timetrex.system.utils.AttendanceImportUtil;

/**
 * 考勤数据导入Controller
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Controller
@RequestMapping("/system/attendance/import")
public class AttendanceImportController extends BaseController
{
    private String prefix = "system/attendance/import";

    @Autowired
    private IAttendanceSummaryService attendanceSummaryService;

    @Autowired
    private IAttendanceDetailService attendanceDetailService;

    @RequiresPermissions("system:attendance:import:view")
    @GetMapping()
    public String attendanceImport()
    {
        return prefix + "/import";
    }

    /**
     * 查询考勤概况列表
     */
    @RequiresPermissions("system:attendance:import:list")
    @PostMapping("/summaryList")
    @ResponseBody
    public TableDataInfo summaryList(AttendanceSummary attendanceSummary)
    {
        startPage();
        List<AttendanceSummary> list = attendanceSummaryService.selectAttendanceSummaryList(attendanceSummary);
        return getDataTable(list);
    }

    /**
     * 查询打卡详情列表
     */
    @RequiresPermissions("system:attendance:import:list")
    @PostMapping("/detailList")
    @ResponseBody
    public TableDataInfo detailList(AttendanceDetail attendanceDetail)
    {
        startPage();
        List<AttendanceDetail> list = attendanceDetailService.selectAttendanceDetailList(attendanceDetail);
        return getDataTable(list);
    }

    /**
     * 导入考勤数据
     */
    @RequiresPermissions("system:attendance:import:import")
    @Log(title = "考勤数据导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile[] files) throws Exception
    {
        if (files == null || files.length == 0) {
            return AjaxResult.error("请选择要导入的文件");
        }

        if (files.length > 3) {
            return AjaxResult.error("一次最多只能上传3个文件");
        }

        // 统计导入结果
        int totalSummaryCount = 0;
        int totalDetailCount = 0;
        List<String> allErrorMessages = new ArrayList<>();
        List<String> allWarningMessages = new ArrayList<>();

        // 处理每个文件
        for (int i = 0; i < files.length; i++) {
            MultipartFile file = files[i];
            String fileName = file.getOriginalFilename();

            try {
                logger.info("开始导入文件: {}, 大小: {} 字节", fileName, file.getSize());

                // 解析Excel文件
                Map<String, Object> importResult = AttendanceImportUtil.importAttendanceData(file.getInputStream());

                if (!(boolean) importResult.get("success")) {
                    List<String> fileErrors = (List<String>) importResult.get("errorMessages");
                    allErrorMessages.add("文件 [" + fileName + "] 导入失败：" + String.join("<br/>", fileErrors));
                    continue;
                }

                List<AttendanceSummary> summaryList = (List<AttendanceSummary>) importResult.get("summaryList");
                List<AttendanceDetail> detailList = (List<AttendanceDetail>) importResult.get("detailList");
                List<String> errorMessages = (List<String>) importResult.get("errorMessages");
                List<String> warningMessages = (List<String>) importResult.get("warningMessages");

                // 保存数据
                if (!summaryList.isEmpty()) {
                    attendanceSummaryService.batchInsertAttendanceSummary(summaryList);
                    totalSummaryCount += summaryList.size();
                }

                if (!detailList.isEmpty()) {
                    attendanceDetailService.batchInsertAttendanceDetail(detailList);
                    totalDetailCount += detailList.size();
                }

                // 添加文件特定的错误和警告信息
                if (!errorMessages.isEmpty()) {
                    allErrorMessages.add("文件 [" + fileName + "] 错误：<br/>" + String.join("<br/>", errorMessages));
                }

                if (!warningMessages.isEmpty()) {
                    allWarningMessages.add("文件 [" + fileName + "] 警告：<br/>" + String.join("<br/>", warningMessages));
                }

                logger.info("文件 [{}] 导入完成，考勤概况: {} 条，打卡详情: {} 条", fileName, summaryList.size(), detailList.size());

            } catch (Exception e) {
                logger.error("导入文件 [" + fileName + "] 时发生异常", e);
                allErrorMessages.add("文件 [" + fileName + "] 导入异常：" + e.getMessage());
            }
        }

        // 如果所有文件都导入失败，返回错误
        if (totalSummaryCount == 0 && totalDetailCount == 0) {
            return AjaxResult.error("所有文件导入失败：<br/>" + String.join("<br/>", allErrorMessages));
        }

        // 构建成功消息
        String message = "导入成功。共导入考勤概况 " + totalSummaryCount + " 条，打卡详情 " + totalDetailCount + " 条。";

        // 添加错误信息
        if (!allErrorMessages.isEmpty()) {
            message += "<br/><span style='color:red'>错误：</span><br/>" + String.join("<br/>", allErrorMessages);
        }

        // 添加警告信息
        if (!allWarningMessages.isEmpty()) {
            message += "<br/><span style='color:orange'>警告：</span><br/>" + String.join("<br/>", allWarningMessages);
        }

        return AjaxResult.success(message);
    }
}
