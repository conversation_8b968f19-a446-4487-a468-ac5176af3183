package com.timetrex.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.timetrex.common.annotation.Excel;
import com.timetrex.common.core.domain.BaseEntity;

/**
 * 人员单价配置对象 staff_unit_price
 * 
 * <AUTHOR>
 * @date 2024-12-20
 */
public class StaffUnitPrice extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 厂商ID */
    @Excel(name = "厂商ID")
    private Long vendorId;

    /** 厂商名称 */
    @Excel(name = "厂商名称")
    private String vendorName;

    /** 员工工号 */
    @Excel(name = "员工工号")
    private String staffNo;

    /** 员工姓名 */
    @Excel(name = "员工姓名")
    private String staffName;

    /** 单价（元/人月） */
    @Excel(name = "单价", readConverterExp = "元/人月")
    private BigDecimal unitPrice;

    /** 生效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "生效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date effectiveDate;

    /** 失效日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "失效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expireDate;

    /** 状态：ACTIVE-有效，INACTIVE-无效 */
    @Excel(name = "状态", readConverterExp = "ACTIVE=有效,INACTIVE=无效")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setVendorId(Long vendorId) 
    {
        this.vendorId = vendorId;
    }

    public Long getVendorId() 
    {
        return vendorId;
    }
    public void setVendorName(String vendorName) 
    {
        this.vendorName = vendorName;
    }

    public String getVendorName() 
    {
        return vendorName;
    }
    public void setStaffNo(String staffNo) 
    {
        this.staffNo = staffNo;
    }

    public String getStaffNo() 
    {
        return staffNo;
    }
    public void setStaffName(String staffName) 
    {
        this.staffName = staffName;
    }

    public String getStaffName() 
    {
        return staffName;
    }
    public void setUnitPrice(BigDecimal unitPrice) 
    {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getUnitPrice() 
    {
        return unitPrice;
    }
    public void setEffectiveDate(Date effectiveDate) 
    {
        this.effectiveDate = effectiveDate;
    }

    public Date getEffectiveDate() 
    {
        return effectiveDate;
    }
    public void setExpireDate(Date expireDate) 
    {
        this.expireDate = expireDate;
    }

    public Date getExpireDate() 
    {
        return expireDate;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("vendorId", getVendorId())
            .append("vendorName", getVendorName())
            .append("staffNo", getStaffNo())
            .append("staffName", getStaffName())
            .append("unitPrice", getUnitPrice())
            .append("effectiveDate", getEffectiveDate())
            .append("expireDate", getExpireDate())
            .append("status", getStatus())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
