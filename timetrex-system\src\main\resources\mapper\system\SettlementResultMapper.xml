<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.timetrex.system.mapper.SettlementResultMapper">

    <resultMap type="SettlementResult" id="SettlementResultResult">
        <result property="id"    column="id"    />
        <result property="staffName"    column="staff_name"    />
        <result property="vendorId"    column="vendor_id"    />
        <result property="vendorName"    column="vendor_name"    />
        <result property="month"    column="month"    />
        <result property="standardHours"    column="standard_hours"    />
        <result property="calculatedHours"    column="calculated_hours"    />
        <result property="workDays"    column="work_days"    />
        <result property="amount"    column="amount"    />
    </resultMap>

    <sql id="selectSettlementResultVo">
        select sr.id, sr.staff_name, sr.vendor_id, v.name as vendor_name,
        sr.month, sr.standard_hours, sr.calculated_hours, sr.work_days, sr.amount
        from settlement_result sr
        left join vendor v on sr.vendor_id = v.id
    </sql>

    <select id="selectSettlementResultList" parameterType="SettlementResult" resultMap="SettlementResultResult">
        <include refid="selectSettlementResultVo"/>
        <where>
            <if test="staffName != null  and staffName != ''"> and sr.staff_name like concat('%', #{staffName}, '%')</if>
            <if test="vendorId != null "> and sr.vendor_id = #{vendorId}</if>
            <if test="vendorName != null  and vendorName != ''"> and v.name like concat('%', #{vendorName}, '%')</if>
            <if test="month != null "> and DATE_FORMAT(sr.month, '%Y-%m') = DATE_FORMAT(#{month}, '%Y-%m')</if>
        </where>
    </select>

    <select id="selectSettlementResultById" parameterType="Long" resultMap="SettlementResultResult">
        <include refid="selectSettlementResultVo"/>
        where sr.id = #{id}
    </select>

    <select id="selectSettlementResultByMonthAndVendor" resultMap="SettlementResultResult">
        <include refid="selectSettlementResultVo"/>
        where DATE_FORMAT(sr.month, '%Y-%m') = DATE_FORMAT(#{month}, '%Y-%m')
        <if test="vendorId != null">
            and sr.vendor_id = #{vendorId}
        </if>
    </select>

    <insert id="insertSettlementResult" parameterType="SettlementResult" useGeneratedKeys="true" keyProperty="id">
        insert into settlement_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="staffName != null">staff_name,</if>
            <if test="vendorId != null">vendor_id,</if>
            <if test="month != null">month,</if>
            <if test="standardHours != null">standard_hours,</if>
            <if test="calculatedHours != null">calculated_hours,</if>
            <if test="workDays != null">work_days,</if>
            <if test="amount != null">amount,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="staffName != null">#{staffName},</if>
            <if test="vendorId != null">#{vendorId},</if>
            <if test="month != null">#{month},</if>
            <if test="standardHours != null">#{standardHours},</if>
            <if test="calculatedHours != null">#{calculatedHours},</if>
            <if test="workDays != null">#{workDays},</if>
            <if test="amount != null">#{amount},</if>
         </trim>
    </insert>

    <insert id="batchInsertSettlementResult" parameterType="java.util.List">
        insert into settlement_result (
            staff_name, vendor_id, month, standard_hours, calculated_hours,
            work_days, amount
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.staffName}, #{item.vendorId}, #{item.month}, #{item.standardHours},
                #{item.calculatedHours}, #{item.workDays}, #{item.amount}
            )
        </foreach>
    </insert>

    <update id="updateSettlementResult" parameterType="SettlementResult">
        update settlement_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="staffName != null">staff_name = #{staffName},</if>
            <if test="vendorId != null">vendor_id = #{vendorId},</if>
            <if test="month != null">month = #{month},</if>
            <if test="standardHours != null">standard_hours = #{standardHours},</if>
            <if test="calculatedHours != null">calculated_hours = #{calculatedHours},</if>
            <if test="workDays != null">work_days = #{workDays},</if>
            <if test="amount != null">amount = #{amount},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSettlementResultById" parameterType="Long">
        delete from settlement_result where id = #{id}
    </delete>

    <delete id="deleteSettlementResultByIds" parameterType="String">
        delete from settlement_result where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteSettlementResultByMonthAndVendor">
        delete from settlement_result
        where DATE_FORMAT(month, '%Y-%m') = DATE_FORMAT(#{month}, '%Y-%m')
        <if test="vendorId != null">
            and vendor_id = #{vendorId}
        </if>
    </delete>
</mapper>
