<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改人员工作量登记')" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-workload-edit">
            <input name="id" th:field="*{staffWorkloadRecord.id}" type="hidden">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">厂商：</label>
                <div class="col-sm-8">
                    <select name="vendorId" class="form-control selectpicker" data-live-search="true" required th:disabled="${!canEdit}">
                        <option value="">请选择厂商</option>
                        <option th:each="vendor : ${vendorList}" th:value="${vendor.id}" th:text="${vendor.name}"
                                th:selected="${vendor.id == staffWorkloadRecord.vendorId}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">员工工号：</label>
                <div class="col-sm-8">
                    <input name="staffNo" id="staffNo" th:field="*{staffWorkloadRecord.staffNo}" class="form-control" type="text" th:readonly="${!canEdit}">
                    <span class="help-block m-b-none" th:if="${canEdit}"><i class="fa fa-info-circle"></i> 输入工号后自动获取员工信息，或者输入姓名自动获取工号</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">员工姓名：</label>
                <div class="col-sm-8">
                    <input name="staffName" id="staffName" th:field="*{staffWorkloadRecord.staffName}" class="form-control" type="text" required th:readonly="${!canEdit}">
                    <span class="help-block m-b-none" th:if="${canEdit}"><i class="fa fa-info-circle"></i> 输入姓名后自动获取工号和厂商信息</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">登记年份：</label>
                <div class="col-sm-8">
                    <select name="recordYear" class="form-control" required th:disabled="${!canEdit}">
                        <option value="">请选择年份</option>
                        <option th:each="year : ${#numbers.sequence(2024, 2026)}"
                                th:value="${year}" th:text="${year}" th:selected="${year == staffWorkloadRecord.recordYear}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">登记月份：</label>
                <div class="col-sm-8">
                    <select name="recordMonth" class="form-control" required th:disabled="${!canEdit}">
                        <option value="">请选择月份</option>
                        <option th:each="month : ${#numbers.sequence(1, 12)}"
                                th:value="${month}" th:text="${month + '月'}" th:selected="${month == staffWorkloadRecord.recordMonth}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">工作小时数：</label>
                <div class="col-sm-8">
                    <input name="workHours" id="workHours" th:field="*{staffWorkloadRecord.workHours}" class="form-control" type="number" step="0.01" min="0" required th:readonly="${!canEdit}">
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 输入工作小时数，系统自动计算工作天数（÷9小时/天）</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">工作天数：</label>
                <div class="col-sm-8">
                    <input name="workDays" id="workDays" th:field="*{staffWorkloadRecord.workDays}" class="form-control" type="number" step="0.01" min="0" max="31" readonly>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 根据工作小时数自动计算（工作小时数 ÷ 9）</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">工作内容描述：</label>
                <div class="col-sm-8">
                    <textarea name="workDescription" th:field="*{staffWorkloadRecord.workDescription}" class="form-control" rows="4" required placeholder="请详细描述本月的主要工作内容..." th:readonly="${!canEdit}"></textarea>
                </div>
            </div>
            <div class="form-group" th:if="${staffWorkloadRecord.personMonths != null}">
                <label class="col-sm-3 control-label">人月数：</label>
                <div class="col-sm-8">
                    <input th:value="${#numbers.formatDecimal(staffWorkloadRecord.personMonths, 1, 4)}" class="form-control" type="text" readonly>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 系统自动计算：工作天数 ÷ 平均计薪工作日</span>
                </div>
            </div>
            <div class="form-group" th:if="${staffWorkloadRecord.unitPrice != null}">
                <label class="col-sm-3 control-label">人员单价：</label>
                <div class="col-sm-8">
                    <input th:value="'¥' + ${#numbers.formatDecimal(staffWorkloadRecord.unitPrice, 1, 2)}" class="form-control" type="text" readonly>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 从单价配置中获取</span>
                </div>
            </div>
            <div class="form-group" th:if="${staffWorkloadRecord.settlementAmount != null}">
                <label class="col-sm-3 control-label">结算金额：</label>
                <div class="col-sm-8">
                    <input th:value="'¥' + ${#numbers.formatDecimal(staffWorkloadRecord.settlementAmount, 1, 2)}" class="form-control" type="text" readonly>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 系统自动计算：人月数 × 人员单价</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">状态：</label>
                <div class="col-sm-8">
                    <span class="label" th:classappend="${staffWorkloadRecord.status == 'DRAFT'} ? 'label-default' : (${staffWorkloadRecord.status == 'SUBMITTED'} ? 'label-info' : (${staffWorkloadRecord.status == 'CALCULATED'} ? 'label-warning' : 'label-success'))">
                        <span th:switch="${staffWorkloadRecord.status}">
                            <span th:case="'DRAFT'">草稿</span>
                            <span th:case="'SUBMITTED'">已提交</span>
                            <span th:case="'CALCULATED'">已计算</span>
                            <span th:case="'APPROVED'">已审批</span>
                            <span th:case="*" th:text="${staffWorkloadRecord.status}"></span>
                        </span>
                    </span>
                </div>
            </div>
            <div class="form-group" th:if="${staffWorkloadRecord.submitTime != null}">
                <label class="col-sm-3 control-label">提交时间：</label>
                <div class="col-sm-8">
                    <input th:value="${#dates.format(staffWorkloadRecord.submitTime, 'yyyy-MM-dd HH:mm:ss')}" class="form-control" type="text" readonly>
                </div>
            </div>
            <div class="form-group" th:if="${staffWorkloadRecord.calculateTime != null}">
                <label class="col-sm-3 control-label">计算时间：</label>
                <div class="col-sm-8">
                    <input th:value="${#dates.format(staffWorkloadRecord.calculateTime, 'yyyy-MM-dd HH:mm:ss')}" class="form-control" type="text" readonly>
                </div>
            </div>
            <div class="form-group" th:if="${staffWorkloadRecord.calculateUser != null}">
                <label class="col-sm-3 control-label">计算人员：</label>
                <div class="col-sm-8">
                    <input th:value="${staffWorkloadRecord.calculateUser}" class="form-control" type="text" readonly>
                </div>
            </div>
            <div class="form-group" th:if="${staffWorkloadRecord.approveTime != null}">
                <label class="col-sm-3 control-label">审批时间：</label>
                <div class="col-sm-8">
                    <input th:value="${#dates.format(staffWorkloadRecord.approveTime, 'yyyy-MM-dd HH:mm:ss')}" class="form-control" type="text" readonly>
                </div>
            </div>
            <div class="form-group" th:if="${staffWorkloadRecord.approveUser != null}">
                <label class="col-sm-3 control-label">审批人员：</label>
                <div class="col-sm-8">
                    <input th:value="${staffWorkloadRecord.approveUser}" class="form-control" type="text" readonly>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remark" th:field="*{staffWorkloadRecord.remark}" class="form-control" rows="2" placeholder="其他需要说明的信息..." th:readonly="${!canEdit}"></textarea>
                </div>
            </div>
            <div class="form-group" th:if="${!canEdit}">
                <div class="col-sm-8 col-sm-offset-3">
                    <div class="alert alert-warning">
                        <i class="fa fa-warning"></i> 当前状态不允许修改
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-select-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/workload/record";
        var canEdit = [[${canEdit}]];

        $("#form-workload-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if (!canEdit) {
                $.modal.alertWarning("当前状态不允许修改");
                return;
            }
            if ($.validate.form()) {
                $.operate.save(prefix, $('#form-workload-edit').serialize());
            }
        }

        $(function() {
            // 初始化下拉框
            $('.selectpicker').selectpicker();

            if (canEdit) {
                // 员工工号输入事件
                $("#staffNo").on('blur', function() {
                    var staffNo = $(this).val().trim();
                    if (staffNo) {
                        getStaffInfoByNo(staffNo);
                    }
                });

                // 员工姓名输入事件
                $("#staffName").on('blur', function() {
                    var staffName = $(this).val().trim();
                    if (staffName) {
                        getStaffInfoByName(staffName);
                    }
                });

                // 工作小时数输入事件
                $("#workHours").on('input', function() {
                    calculateWorkDays();
                });
            }
        });

        // 根据工号获取员工信息
        function getStaffInfoByNo(staffNo) {
            $.ajax({
                url: ctx + "system/staff_info/getByStaffNo",
                type: "GET",
                data: { staffNo: staffNo },
                success: function(result) {
                    if (result.code == 0 && result.data) {
                        var staff = result.data;
                        $("#staffName").val(staff.name);
                        $("select[name='vendorId']").val(staff.vendorId);
                        $('.selectpicker').selectpicker('refresh');
                    } else {
                        $.modal.alertWarning("未找到该工号的员工信息");
                    }
                },
                error: function() {
                    $.modal.alertError("获取员工信息失败");
                }
            });
        }

        // 根据姓名获取员工信息
        function getStaffInfoByName(staffName) {
            $.ajax({
                url: ctx + "system/staff_info/getByStaffName",
                type: "GET",
                data: { staffName: staffName },
                success: function(result) {
                    if (result.code == 0 && result.data) {
                        var staff = result.data;
                        $("#staffNo").val(staff.staffNo);
                        $("select[name='vendorId']").val(staff.vendorId);
                        $('.selectpicker').selectpicker('refresh');
                    } else {
                        $.modal.alertWarning("未找到该姓名的员工信息");
                    }
                },
                error: function() {
                    $.modal.alertError("获取员工信息失败");
                }
            });
        }

        // 计算工作天数
        function calculateWorkDays() {
            var workHours = parseFloat($("#workHours").val()) || 0;
            var workDays = workHours / 9; // 每天9小时
            $("#workDays").val(workDays.toFixed(2));
        }
    </script>
</body>
</html>
