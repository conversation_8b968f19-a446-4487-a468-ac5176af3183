package com.timetrex.common.utils;

import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 年月字符串转LocalDate转换器
 * 
 * <AUTHOR>
 */
@Component
public class YearMonthConverter implements Converter<String, LocalDate> {

    private static final DateTimeFormatter FORMATTER_YYYY_MM = DateTimeFormatter.ofPattern("yyyy-MM");
    private static final DateTimeFormatter FORMATTER_YYYY_MM_DD = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public LocalDate convert(String source) {
        if (source == null || source.isEmpty()) {
            return null;
        }
        
        source = source.trim();
        
        try {
            // 尝试按yyyy-MM-dd格式解析
            return LocalDate.parse(source, FORMATTER_YYYY_MM_DD);
        } catch (DateTimeParseException e) {
            try {
                // 尝试按yyyy-MM格式解析，并设置为当月第一天
                return LocalDate.parse(source + "-01", FORMATTER_YYYY_MM_DD);
            } catch (DateTimeParseException ex) {
                throw new IllegalArgumentException("无法解析日期: " + source, ex);
            }
        }
    }
}
