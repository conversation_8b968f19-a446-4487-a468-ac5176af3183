package com.timetrex.system.service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import com.timetrex.system.domain.QuarterlyReport;

/**
 * 季度归总报表Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface IQuarterlyReportService 
{
    /**
     * 生成季度归总报表
     * 
     * @param year 年份
     * @param quarter 季度（1-4）
     * @param vendorIds 厂商ID列表
     * @return 季度归总报表
     */
    public List<QuarterlyReport> generateQuarterlyReport(int year, int quarter, List<Long> vendorIds);
    
    /**
     * 获取季度的起止日期
     * 
     * @param year 年份
     * @param quarter 季度（1-4）
     * @return 包含起始日期和结束日期的Map
     */
    public Map<String, LocalDate> getQuarterDateRange(int year, int quarter);
    
    /**
     * 导出季度归总报表
     * 
     * @param year 年份
     * @param quarter 季度（1-4）
     * @param vendorIds 厂商ID列表
     * @return 导出文件路径
     */
    public String exportQuarterlyReport(int year, int quarter, List<Long> vendorIds);
}
