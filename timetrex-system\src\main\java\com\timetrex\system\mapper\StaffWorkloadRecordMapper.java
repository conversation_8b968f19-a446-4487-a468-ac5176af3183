package com.timetrex.system.mapper;

import java.util.List;
import com.timetrex.system.domain.StaffWorkloadRecord;

/**
 * 人员工作量登记Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface StaffWorkloadRecordMapper 
{
    /**
     * 查询人员工作量登记
     * 
     * @param id 人员工作量登记主键
     * @return 人员工作量登记
     */
    public StaffWorkloadRecord selectStaffWorkloadRecordById(Long id);

    /**
     * 查询人员工作量登记列表
     * 
     * @param staffWorkloadRecord 人员工作量登记
     * @return 人员工作量登记集合
     */
    public List<StaffWorkloadRecord> selectStaffWorkloadRecordList(StaffWorkloadRecord staffWorkloadRecord);

    /**
     * 新增人员工作量登记
     * 
     * @param staffWorkloadRecord 人员工作量登记
     * @return 结果
     */
    public int insertStaffWorkloadRecord(StaffWorkloadRecord staffWorkloadRecord);

    /**
     * 修改人员工作量登记
     * 
     * @param staffWorkloadRecord 人员工作量登记
     * @return 结果
     */
    public int updateStaffWorkloadRecord(StaffWorkloadRecord staffWorkloadRecord);

    /**
     * 删除人员工作量登记
     * 
     * @param id 人员工作量登记主键
     * @return 结果
     */
    public int deleteStaffWorkloadRecordById(Long id);

    /**
     * 批量删除人员工作量登记
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStaffWorkloadRecordByIds(Long[] ids);

    /**
     * 根据厂商ID和年月查询工作量登记
     * 
     * @param vendorId 厂商ID
     * @param recordYear 年份
     * @param recordMonth 月份
     * @return 工作量登记列表
     */
    public List<StaffWorkloadRecord> selectByVendorAndMonth(Long vendorId, Integer recordYear, Integer recordMonth);

    /**
     * 根据员工工号和年月查询工作量登记
     * 
     * @param staffNo 员工工号
     * @param recordYear 年份
     * @param recordMonth 月份
     * @return 工作量登记
     */
    public StaffWorkloadRecord selectByStaffAndMonth(String staffNo, Integer recordYear, Integer recordMonth);

    /**
     * 批量更新状态
     * 
     * @param ids 主键集合
     * @param status 状态
     * @param updateBy 更新人
     * @return 结果
     */
    public int batchUpdateStatus(Long[] ids, String status, String updateBy);

    /**
     * 查询待计算的工作量登记
     * 
     * @param vendorId 厂商ID
     * @param recordYear 年份
     * @param recordMonth 月份
     * @return 工作量登记列表
     */
    public List<StaffWorkloadRecord> selectPendingCalculation(Long vendorId, Integer recordYear, Integer recordMonth);
}
