package com.timetrex.system.service;

import java.util.List;
import com.timetrex.system.domain.StaffInfo;

/**
 * 人员信息Service接口
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface IStaffInfoService
{
    /**
     * 查询人员信息
     *
     * @param id 人员信息主键
     * @return 人员信息
     */
    public StaffInfo selectStaffInfoById(Long id);

    /**
     * 查询人员信息列表
     *
     * @param staffInfo 人员信息
     * @return 人员信息集合
     */
    public List<StaffInfo> selectStaffInfoList(StaffInfo staffInfo);

    /**
     * 新增人员信息
     *
     * @param staffInfo 人员信息
     * @return 结果
     */
    public int insertStaffInfo(StaffInfo staffInfo);

    /**
     * 修改人员信息
     *
     * @param staffInfo 人员信息
     * @return 结果
     */
    public int updateStaffInfo(StaffInfo staffInfo);

    /**
     * 批量删除人员信息
     *
     * @param ids 需要删除的人员信息主键集合
     * @return 结果
     */
    public int deleteStaffInfoByIds(String ids);

    /**
     * 删除人员信息信息
     *
     * @param id 人员信息主键
     * @return 结果
     */
    public int deleteStaffInfoById(Long id);

    /**
     * 根据员工工号查询人员信息
     *
     * @param staffNo 员工工号
     * @return 人员信息
     */
    public StaffInfo selectStaffInfoByStaffNo(String staffNo);

    /**
     * 根据厂商ID查询在职人员列表
     *
     * @param vendorId 厂商ID
     * @return 人员信息集合
     */
    public List<StaffInfo> selectActiveStaffByVendorId(Long vendorId);
}
