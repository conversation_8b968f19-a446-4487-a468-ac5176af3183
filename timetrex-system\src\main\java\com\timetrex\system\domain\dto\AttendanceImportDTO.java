package com.timetrex.system.domain.dto;

import java.util.Date;
import java.util.List;

/**
 * 考勤概况导入DTO
 */
public class AttendanceImportDTO {
    private Date date;
    private String name;
    private String account;
    private String department;
    private String position;
    private String staffId;
    private String ruleName;
    private String shift;
    private Date earliestTime;
    private Date latestTime;
    private Integer punchCount;
    private Double standardHours;
    private Double actualHours;
    
    // Getters and Setters
    public Date getDate() {
        return date;
    }
    public void setDate(Date date) {
        this.date = date;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public String getAccount() {
        return account;
    }
    public void setAccount(String account) {
        this.account = account;
    }
    public String getDepartment() {
        return department;
    }
    public void setDepartment(String department) {
        this.department = department;
    }
    public String getPosition() {
        return position;
    }
    public void setPosition(String position) {
        this.position = position;
    }
    public String getStaffId() {
        return staffId;
    }
    public void setStaffId(String staffId) {
        this.staffId = staffId;
    }
    public String getRuleName() {
        return ruleName;
    }
    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }
    public String getShift() {
        return shift;
    }
    public void setShift(String shift) {
        this.shift = shift;
    }
    public Date getEarliestTime() {
        return earliestTime;
    }
    public void setEarliestTime(Date earliestTime) {
        this.earliestTime = earliestTime;
    }
    public Date getLatestTime() {
        return latestTime;
    }
    public void setLatestTime(Date latestTime) {
        this.latestTime = latestTime;
    }
    public Integer getPunchCount() {
        return punchCount;
    }
    public void setPunchCount(Integer punchCount) {
        this.punchCount = punchCount;
    }
    public Double getStandardHours() {
        return standardHours;
    }
    public void setStandardHours(Double standardHours) {
        this.standardHours = standardHours;
    }
    public Double getActualHours() {
        return actualHours;
    }
    public void setActualHours(Double actualHours) {
        this.actualHours = actualHours;
    }
}