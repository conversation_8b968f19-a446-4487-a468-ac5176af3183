package com.timetrex.system.utils;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * Excel列映射工具类
 * 用于根据列标题动态映射Excel列索引
 * 
 * <AUTHOR>
 */
public class ExcelColumnMapper {
    
    private static final Logger log = LoggerFactory.getLogger(ExcelColumnMapper.class);
    
    // 列标题与索引的映射
    private final Map<String, Integer> columnMap = new HashMap<>();
    
    // 必需的列标题
    private final String[] requiredColumns;
    
    // 是否初始化成功
    private boolean initialized = false;
    
    /**
     * 构造函数
     * 
     * @param requiredColumns 必需的列标题数组
     */
    public ExcelColumnMapper(String[] requiredColumns) {
        this.requiredColumns = requiredColumns;
    }
    
    /**
     * 初始化列映射
     * 
     * @param headerRow 标题行
     * @return 是否初始化成功
     */
    public boolean initialize(Row headerRow) {
        if (headerRow == null) {
            log.error("标题行为空，无法初始化列映射");
            return false;
        }
        
        // 清空之前的映射
        columnMap.clear();
        
        // 遍历标题行，建立列标题与索引的映射
        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            Cell cell = headerRow.getCell(i);
            if (cell != null && cell.getCellType() == org.apache.poi.ss.usermodel.CellType.STRING) {
                String columnTitle = cell.getStringCellValue().trim();
                if (!columnTitle.isEmpty()) {
                    columnMap.put(columnTitle, i);
                    log.debug("映射列: {} -> 索引: {}", columnTitle, i);
                }
            }
        }
        
        // 检查是否包含所有必需的列
        boolean allRequired = true;
        for (String requiredColumn : requiredColumns) {
            if (!columnMap.containsKey(requiredColumn)) {
                log.error("缺少必需的列: {}", requiredColumn);
                allRequired = false;
            }
        }
        
        initialized = allRequired;
        return initialized;
    }
    
    /**
     * 获取列索引
     * 
     * @param columnTitle 列标题
     * @return 列索引，如果列不存在则返回-1
     */
    public int getColumnIndex(String columnTitle) {
        if (!initialized) {
            log.error("列映射未初始化");
            return -1;
        }
        
        Integer index = columnMap.get(columnTitle);
        return index != null ? index : -1;
    }
    
    /**
     * 获取单元格
     * 
     * @param row 行
     * @param columnTitle 列标题
     * @return 单元格，如果列不存在则返回null
     */
    public Cell getCell(Row row, String columnTitle) {
        if (row == null) {
            return null;
        }
        
        int index = getColumnIndex(columnTitle);
        if (index == -1) {
            return null;
        }
        
        return row.getCell(index);
    }
    
    /**
     * 是否初始化成功
     * 
     * @return 是否初始化成功
     */
    public boolean isInitialized() {
        return initialized;
    }
    
    /**
     * 获取所有列映射
     * 
     * @return 列映射
     */
    public Map<String, Integer> getColumnMap() {
        return new HashMap<>(columnMap);
    }
}
