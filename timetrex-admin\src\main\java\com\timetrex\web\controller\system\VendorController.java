package com.timetrex.web.controller.system;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.timetrex.common.annotation.Log;
import com.timetrex.common.enums.BusinessType;
import com.timetrex.system.domain.Vendor;
import com.timetrex.system.service.IVendorService;
import com.timetrex.common.core.controller.BaseController;
import com.timetrex.common.core.domain.AjaxResult;
import com.timetrex.common.utils.poi.ExcelUtil;
import com.timetrex.common.core.page.TableDataInfo;

/**
 * 厂商配置Controller
 * 
 * <AUTHOR>
 * @date 2025-04-15
 */
@Controller
@RequestMapping("/system/vendor")
public class VendorController extends BaseController
{
    private String prefix = "system/vendor";

    @Autowired
    private IVendorService vendorService;

    @RequiresPermissions("system:vendor:view")
    @GetMapping()
    public String vendor()
    {
        return prefix + "/vendor";
    }

    /**
     * 查询厂商配置列表
     */
    @RequiresPermissions("system:vendor:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Vendor vendor)
    {
        startPage();
        List<Vendor> list = vendorService.selectVendorList(vendor);
        return getDataTable(list);
    }

    /**
     * 导出厂商配置列表
     */
    @RequiresPermissions("system:vendor:export")
    @Log(title = "厂商配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Vendor vendor)
    {
        List<Vendor> list = vendorService.selectVendorList(vendor);
        ExcelUtil<Vendor> util = new ExcelUtil<Vendor>(Vendor.class);
        return util.exportExcel(list, "厂商配置数据");
    }

    /**
     * 新增厂商配置
     */
    @RequiresPermissions("system:vendor:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存厂商配置
     */
    @RequiresPermissions("system:vendor:add")
    @Log(title = "厂商配置", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(Vendor vendor)
    {
        return toAjax(vendorService.insertVendor(vendor));
    }

    /**
     * 修改厂商配置
     */
    @RequiresPermissions("system:vendor:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        Vendor vendor = vendorService.selectVendorById(id);
        mmap.put("vendor", vendor);
        return prefix + "/edit";
    }

    /**
     * 修改保存厂商配置
     */
    @RequiresPermissions("system:vendor:edit")
    @Log(title = "厂商配置", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(Vendor vendor)
    {
        return toAjax(vendorService.updateVendor(vendor));
    }

    /**
     * 删除厂商配置
     */
    @RequiresPermissions("system:vendor:remove")
    @Log(title = "厂商配置", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(vendorService.deleteVendorByIds(ids));
    }
}
