package com.timetrex.system.domain;

import java.time.LocalDate;
import java.time.LocalTime;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.timetrex.common.annotation.Excel;
import com.timetrex.common.core.domain.BaseEntity;

/**
 * 打卡详情对象 attendance_detail
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
public class AttendanceDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 账号 */
    @Excel(name = "账号")
    private String staffNo;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "日期", width = 30, dateFormat = "yyyy-MM-dd")
    private LocalDate checkDate;

    /** 部门 */
    @Excel(name = "部门")
    private String department;

    /** 职务 */
    @Excel(name = "职务")
    private String position;

    /** 所属规则 */
    @Excel(name = "所属规则")
    private String workRule;

    /** 打卡类型(上班/下班) */
    @Excel(name = "打卡类型", readConverterExp = "上=班/下班")
    private String checkType;

    /** 应打卡时间 */
    @JsonFormat(pattern = "HH:mm:ss")
    @Excel(name = "应打卡时间", width = 30, dateFormat = "HH:mm:ss")
    private LocalTime scheduledTime;

    /** 实际打卡时间 */
    @JsonFormat(pattern = "HH:mm:ss")
    @Excel(name = "实际打卡时间", width = 30, dateFormat = "HH:mm:ss")
    private LocalTime actualTime;

    /** 打卡状态(正常/迟到/早退等) */
    @Excel(name = "打卡状态", readConverterExp = "正=常/迟到/早退等")
    private String checkStatus;

    /** 打卡地点 */
    @Excel(name = "打卡地点")
    private String location;

    /** 打卡设备 */
    @Excel(name = "打卡设备")
    private String deviceId;

    /** 备注内容 */
    @Excel(name = "备注内容")
    private String remark;

    /** 备注图片路径 */
    private String remarkImage;

    /** 假勤申请内容 */
    @Excel(name = "假勤申请内容")
    private String leaveApplication;

    /** 星期几 */
    @Excel(name = "星期几")
    private String dayOfWeek;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setStaffNo(String staffNo)
    {
        this.staffNo = staffNo;
    }

    public String getStaffNo()
    {
        return staffNo;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setCheckDate(LocalDate checkDate)
    {
        this.checkDate = checkDate;
    }

    public LocalDate getCheckDate()
    {
        return checkDate;
    }
    public void setDepartment(String department)
    {
        this.department = department;
    }

    public String getDepartment()
    {
        return department;
    }
    public void setPosition(String position)
    {
        this.position = position;
    }

    public String getPosition()
    {
        return position;
    }
    public void setWorkRule(String workRule)
    {
        this.workRule = workRule;
    }

    public String getWorkRule()
    {
        return workRule;
    }
    public void setCheckType(String checkType)
    {
        this.checkType = checkType;
    }

    public String getCheckType()
    {
        return checkType;
    }
    public void setScheduledTime(LocalTime scheduledTime)
    {
        this.scheduledTime = scheduledTime;
    }

    public LocalTime getScheduledTime()
    {
        return scheduledTime;
    }
    public void setActualTime(LocalTime actualTime)
    {
        this.actualTime = actualTime;
    }

    public LocalTime getActualTime()
    {
        return actualTime;
    }
    public void setCheckStatus(String checkStatus)
    {
        this.checkStatus = checkStatus;
    }

    public String getCheckStatus()
    {
        return checkStatus;
    }
    public void setLocation(String location)
    {
        this.location = location;
    }

    public String getLocation()
    {
        return location;
    }
    public void setDeviceId(String deviceId)
    {
        this.deviceId = deviceId;
    }

    public String getDeviceId()
    {
        return deviceId;
    }
    public void setRemarkImage(String remarkImage)
    {
        this.remarkImage = remarkImage;
    }

    public String getRemarkImage()
    {
        return remarkImage;
    }
    public void setLeaveApplication(String leaveApplication)
    {
        this.leaveApplication = leaveApplication;
    }

    public String getLeaveApplication()
    {
        return leaveApplication;
    }

    public void setDayOfWeek(String dayOfWeek)
    {
        this.dayOfWeek = dayOfWeek;
    }

    public String getDayOfWeek()
    {
        return dayOfWeek;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("staffNo", getStaffNo())
            .append("name", getName())
            .append("checkDate", getCheckDate())
            .append("department", getDepartment())
            .append("position", getPosition())
            .append("workRule", getWorkRule())
            .append("checkType", getCheckType())
            .append("scheduledTime", getScheduledTime())
            .append("actualTime", getActualTime())
            .append("checkStatus", getCheckStatus())
            .append("location", getLocation())
            .append("deviceId", getDeviceId())
            .append("remark", getRemark())
            .append("remarkImage", getRemarkImage())
            .append("leaveApplication", getLeaveApplication())
            .append("dayOfWeek", getDayOfWeek())
            .toString();
    }
}
