package com.timetrex.system.mapper;

import java.time.LocalDate;
import java.util.List;
import com.timetrex.system.domain.AttendanceSummary;

/**
 * 考勤概况统计与打卡明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-09
 */
public interface AttendanceSummaryMapper 
{
    /**
     * 查询考勤概况统计与打卡明细
     * 
     * @param id 考勤概况统计与打卡明细主键
     * @return 考勤概况统计与打卡明细
     */
    public AttendanceSummary selectAttendanceSummaryById(Long id);

    /**
     * 查询考勤概况统计与打卡明细列表
     * 
     * @param attendanceSummary 考勤概况统计与打卡明细
     * @return 考勤概况统计与打卡明细集合
     */
    public List<AttendanceSummary> selectAttendanceSummaryList(AttendanceSummary attendanceSummary);
    
    /**
     * 根据月份和员工编号查询考勤概况
     * 
     * @param month 月份
     * @param staffNo 员工编号
     * @return 考勤概况统计集合
     */
    public List<AttendanceSummary> selectAttendanceSummaryByMonthAndStaffNo(LocalDate month, String staffNo);
    
    /**
     * 根据月份查询考勤概况
     * 
     * @param month 月份
     * @return 考勤概况统计集合
     */
    public List<AttendanceSummary> selectAttendanceSummaryByMonth(LocalDate month);

    /**
     * 新增考勤概况统计与打卡明细
     * 
     * @param attendanceSummary 考勤概况统计与打卡明细
     * @return 结果
     */
    public int insertAttendanceSummary(AttendanceSummary attendanceSummary);
    
    /**
     * 批量新增考勤概况
     * 
     * @param attendanceSummaryList 考勤概况列表
     * @return 结果
     */
    public int batchInsertAttendanceSummary(List<AttendanceSummary> attendanceSummaryList);

    /**
     * 修改考勤概况统计与打卡明细
     * 
     * @param attendanceSummary 考勤概况统计与打卡明细
     * @return 结果
     */
    public int updateAttendanceSummary(AttendanceSummary attendanceSummary);

    /**
     * 删除考勤概况统计与打卡明细
     * 
     * @param id 考勤概况统计与打卡明细主键
     * @return 结果
     */
    public int deleteAttendanceSummaryById(Long id);

    /**
     * 批量删除考勤概况统计与打卡明细
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAttendanceSummaryByIds(String[] ids);
}
