# 外包结算系统功能说明文档

## 2. 外包核算
### 2.4 考勤数据导入（菜单一）
- Excel导入功能：
  - 支持两个sheet页导入：
    - Sheet1：概况统计与打卡明细
    - Sheet2：打卡详情
  - 字段映射：
    - 概况统计：时间、姓名、账号、部门、职务、工号、所属规则、班次、最早、最晚、打卡次数、标准工作时长、实际工作时长
    - 打卡详情：全部字段导入
  - 数据关联：通过账号字段关联两个sheet数据
  - 异常处理：检测同一设备关联多人时给出软提示

### 2.5 考勤核算（菜单二）
- 核算规则：
  1. 按月份、厂商筛选数据
  2. 生成汇总表：
     - 字段：姓名、标准工作时长、实际打卡按9小时算
     - 计算规则：
       - 实际打卡按9小时算：满9小时按9小时，不满按4小时
       - excel人月工时公式：ROUND(IF(实际打卡=0,0,IF(实际>标准,标准/9/avg_days,实际/9/avg_days)),2)
       - 结算金额：人月工时 × 单价（单价在人员信息中维护）
  3. 结果存储到核算结果表
  4.核算结果表可以导出并发送指定的邮箱
  5.执行核算是可以同时选择多厂商一起核算
  
## 3. 数据库设计
### 3.1 建表语句

```sql
CREATE TABLE `attendance_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_no` varchar(50) NOT NULL COMMENT '账号',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `check_date` date NOT NULL COMMENT '日期',
  `day_of_week` varchar(10) DEFAULT NULL COMMENT '星期几',
  `department` varchar(100) DEFAULT NULL COMMENT '部门',
  `position` varchar(100) DEFAULT NULL COMMENT '职务',
  `work_rule` varchar(100) DEFAULT NULL COMMENT '所属规则',
  `check_type` varchar(20) DEFAULT NULL COMMENT '打卡类型(上班/下班)',
  `scheduled_time` time DEFAULT NULL COMMENT '应打卡时间',
  `actual_time` time DEFAULT NULL COMMENT '实际打卡时间',
  `check_status` varchar(20) DEFAULT NULL COMMENT '打卡状态(正常/迟到/早退等)',
  `location` varchar(100) DEFAULT NULL COMMENT '打卡地点',
  `device_id` varchar(255) DEFAULT NULL COMMENT '打卡设备',
  `remark` text COMMENT '备注内容',
  `remark_image` varchar(255) DEFAULT NULL COMMENT '备注图片路径',
  `leave_application` text COMMENT '假勤申请内容',
  PRIMARY KEY (`id`),
  KEY `idx_staff_no` (`staff_no`),
  KEY `idx_check_date` (`check_date`),
  KEY `idx_actual_time` (`actual_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='打卡详情表';

CREATE TABLE `attendance_summary` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_no` varchar(50) NOT NULL COMMENT '账号',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `record_date` date NOT NULL COMMENT '时间',
  `day_of_week` varchar(10) DEFAULT NULL COMMENT '星期几',
  `department` varchar(100) DEFAULT NULL COMMENT '部门',
  `position` varchar(100) DEFAULT NULL COMMENT '职务',
  `work_rule` varchar(100) DEFAULT NULL COMMENT '所属规则',
  `shift` varchar(50) DEFAULT NULL COMMENT '班次',
  `earliest_check` time DEFAULT NULL COMMENT '最早打卡时间',
  `latest_check` time DEFAULT NULL COMMENT '最晚打卡时间',
  `check_count` int(11) DEFAULT NULL COMMENT '打卡次数(次)',
  `standard_hours` decimal(5,2) DEFAULT NULL COMMENT '标准工作时长(小时)',
  `actual_hours` decimal(5,2) DEFAULT NULL COMMENT '实际工作时长(小时)',
  `calculated_hours` decimal(5,2) DEFAULT NULL COMMENT '实际打卡按9小时算',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_staff_date` (`staff_no`,`record_date`),
  KEY `idx_record_date` (`record_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='考勤概况统计与打卡明细';

CREATE TABLE `settlement_result` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_name` varchar(50) NOT NULL COMMENT '人员姓名',
  `vendor_id` int(11) NOT NULL COMMENT '厂商ID',
  `month` date NOT NULL COMMENT '核算月份',
  `standard_hours` decimal(10,2) DEFAULT NULL COMMENT '标准工作时长(小时)',
  `calculated_hours` decimal(10,2) DEFAULT NULL COMMENT '实际打卡按9小时算',
  `work_days` decimal(10,2) DEFAULT NULL COMMENT '人月工时',
  `amount` decimal(10,2) DEFAULT NULL COMMENT '结算金额',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_staffname_vendor_month` (`staff_name`,`vendor_id`,`month`),
  KEY `idx_month` (`month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='核算结果表';

CREATE TABLE `staff_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_no` varchar(50) NOT NULL COMMENT '工号',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `vendor_id` int(11) NOT NULL COMMENT '所属厂商ID',
  `project_id` int(11) DEFAULT NULL COMMENT '所属项目ID',
  `role_id` int(11) DEFAULT NULL COMMENT '角色ID',
  `experience_years` decimal(10,1) DEFAULT NULL COMMENT '工作经验年限',
  `unit_price` decimal(10,2) NOT NULL COMMENT '单价(元/人月)',
  `staff_level` int(11) DEFAULT NULL COMMENT '人员级别',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(1-在职 0-离职)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_staff_no` (`staff_no`),
  KEY `idx_vendor_id` (`vendor_id`)
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4 COMMENT='人员信息表';

CREATE TABLE `vendor` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '厂商名称',
  `avg_work_days` decimal(5,2) NOT NULL DEFAULT '20.83' COMMENT '平均计薪工作日',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COMMENT='厂商配置表';
```