package com.timetrex.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.timetrex.common.annotation.Excel;
import com.timetrex.common.core.domain.BaseEntity;

/**
 * 人员工作量登记对象 staff_workload_record
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
public class StaffWorkloadRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 厂商ID（从人员信息中获取） */
    @Excel(name = "厂商ID")
    private Long vendorId;

    /** 厂商名称（从厂商管理中获取） */
    @Excel(name = "厂商名称")
    private String vendorName;

    /** 员工工号 */
    @Excel(name = "员工工号")
    private String staffNo;

    /** 员工姓名 */
    @Excel(name = "员工姓名")
    private String staffName;

    /** 登记年份 */
    @Excel(name = "登记年份")
    private Integer recordYear;

    /** 登记月份 */
    @Excel(name = "登记月份")
    private Integer recordMonth;

    /** 工作内容描述 */
    @Excel(name = "工作内容描述")
    private String workDescription;

    /** 工作天数 */
    @Excel(name = "工作天数")
    private BigDecimal workDays;

    /** 工作小时数 */
    @Excel(name = "工作小时数")
    private BigDecimal workHours;

    /** 人月数（工作天数/平均计薪工作日） */
    @Excel(name = "人月数")
    private BigDecimal personMonths;

    /** 人员单价（从人员信息中获取） */
    @Excel(name = "人员单价", readConverterExp = "元/人月")
    private BigDecimal unitPrice;

    /** 结算金额（人月数*单价） */
    @Excel(name = "结算金额")
    private BigDecimal settlementAmount;

    /** 状态：DRAFT-草稿，SUBMITTED-已提交，CALCULATED-已计算，APPROVED-已审批 */
    @Excel(name = "状态", readConverterExp = "DRAFT=草稿,SUBMITTED=已提交,CALCULATED=已计算,APPROVED=已审批")
    private String status;

    /** 提交时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    /** 计算时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "计算时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date calculateTime;

    /** 计算人员 */
    @Excel(name = "计算人员")
    private String calculateUser;

    /** 审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审批时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    /** 审批人员 */
    @Excel(name = "审批人员")
    private String approveUser;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setVendorId(Long vendorId)
    {
        this.vendorId = vendorId;
    }

    public Long getVendorId()
    {
        return vendorId;
    }
    public void setVendorName(String vendorName)
    {
        this.vendorName = vendorName;
    }

    public String getVendorName()
    {
        return vendorName;
    }
    public void setStaffNo(String staffNo)
    {
        this.staffNo = staffNo;
    }

    public String getStaffNo()
    {
        return staffNo;
    }
    public void setStaffName(String staffName)
    {
        this.staffName = staffName;
    }

    public String getStaffName()
    {
        return staffName;
    }
    public void setRecordYear(Integer recordYear)
    {
        this.recordYear = recordYear;
    }

    public Integer getRecordYear()
    {
        return recordYear;
    }
    public void setRecordMonth(Integer recordMonth)
    {
        this.recordMonth = recordMonth;
    }

    public Integer getRecordMonth()
    {
        return recordMonth;
    }
    public void setWorkDescription(String workDescription)
    {
        this.workDescription = workDescription;
    }

    public String getWorkDescription()
    {
        return workDescription;
    }
    public void setWorkDays(BigDecimal workDays)
    {
        this.workDays = workDays;
    }

    public BigDecimal getWorkDays()
    {
        return workDays;
    }
    public void setWorkHours(BigDecimal workHours)
    {
        this.workHours = workHours;
    }

    public BigDecimal getWorkHours()
    {
        return workHours;
    }
    public void setPersonMonths(BigDecimal personMonths)
    {
        this.personMonths = personMonths;
    }

    public BigDecimal getPersonMonths()
    {
        return personMonths;
    }
    public void setUnitPrice(BigDecimal unitPrice)
    {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getUnitPrice()
    {
        return unitPrice;
    }
    public void setSettlementAmount(BigDecimal settlementAmount)
    {
        this.settlementAmount = settlementAmount;
    }

    public BigDecimal getSettlementAmount()
    {
        return settlementAmount;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setSubmitTime(Date submitTime)
    {
        this.submitTime = submitTime;
    }

    public Date getSubmitTime()
    {
        return submitTime;
    }
    public void setCalculateTime(Date calculateTime)
    {
        this.calculateTime = calculateTime;
    }

    public Date getCalculateTime()
    {
        return calculateTime;
    }
    public void setCalculateUser(String calculateUser)
    {
        this.calculateUser = calculateUser;
    }

    public String getCalculateUser()
    {
        return calculateUser;
    }
    public void setApproveTime(Date approveTime)
    {
        this.approveTime = approveTime;
    }

    public Date getApproveTime()
    {
        return approveTime;
    }
    public void setApproveUser(String approveUser)
    {
        this.approveUser = approveUser;
    }

    public String getApproveUser()
    {
        return approveUser;
    }
    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("vendorId", getVendorId())
            .append("vendorName", getVendorName())
            .append("staffNo", getStaffNo())
            .append("staffName", getStaffName())
            .append("recordYear", getRecordYear())
            .append("recordMonth", getRecordMonth())
            .append("workDescription", getWorkDescription())
            .append("workDays", getWorkDays())
            .append("workHours", getWorkHours())
            .append("personMonths", getPersonMonths())
            .append("unitPrice", getUnitPrice())
            .append("settlementAmount", getSettlementAmount())
            .append("status", getStatus())
            .append("submitTime", getSubmitTime())
            .append("calculateTime", getCalculateTime())
            .append("calculateUser", getCalculateUser())
            .append("approveTime", getApproveTime())
            .append("approveUser", getApproveUser())
            .append("delFlag", getDelFlag())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
