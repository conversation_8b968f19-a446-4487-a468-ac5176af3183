# 外包结算系统需求文档

## 1. 系统概述
本系统用于管理外包人员的考勤数据并自动计算结算金额，主要功能包括：
- 用户登录与权限管理
- 基础数据维护
- 考勤数据导入
- 考勤核算与结算

## 2. 功能模块说明

### 2.1 用户登录
- 登录流程：
  1. 输入用户名密码
  2. 选择图形验证码
  3. 验证通过后进入系统首页
- 密码加密存储
- 登录成功后显示当前用户和时间

### 2.2 系统首页
- 布局：
  - 顶部：顶部导航栏（Header）
    - 左侧：固定显示系统名称（如“XX管理系统”）。
    - 右侧：显示当前登录用户姓名 + 实时动态时间（格式示例：张三 | 2024-07-25 14:30:00）。
  - 主体内容区（Main Content）
    - 左侧菜单栏（Sidebar）
      - 根据用户权限动态生成菜单项（如管理员显示全部菜单，普通用户显示部分菜单）。
      -菜单支持多级嵌套（可折叠展开）。
    - 右侧操作区（Workspace）
      - 默认状态：显示欢迎信息（如“欢迎使用XX系统”）。
      - 交互逻辑：
        - 点击左侧菜单后，在右侧操作区动态加载对应功能模块。
        - 每个功能模块以**标签页（Tabs）**形式横向平铺展示，支持：
          - 点击标签切换内容。
          - 关闭单个标签（通过标签右侧的 × 按钮）。
          - 标签过多时可横向滚动。

### 2.3 基础数据维护
1. 人员信息维护：
   - 归属厂商、项目、角色
   - 工作经验年限、单价、状态
2. 厂商配置：
   - 维护"平均计薪工作日"（默认值20.83）

### 2.4 考勤数据导入（菜单一）
- Excel导入功能：
  - 支持两个sheet页导入：
    - Sheet1：概况统计与打卡明细
    - Sheet2：打卡详情
  - 字段映射：
    - 概况统计：时间、姓名、账号、部门、职务、工号、所属规则、班次、最早、最晚、打卡次数、标准工作时长、实际工作时长
    - 打卡详情：全部字段导入
  - 数据关联：通过账号字段关联两个sheet数据
  - 异常处理：检测同一设备关联多人时给出软提示

### 2.5 考勤核算（菜单二）
- 核算规则：
  1. 按月份、厂商筛选数据
  2. 生成汇总表：
     - 字段：姓名、标准工作时长、实际打卡按9小时算
     - 计算规则：
       - 实际打卡按9小时算：满9小时按9小时，不满按4小时
       - 人月工时公式：ROUND(IF(实际打卡=0,0,IF(实际>标准,标准/9/avg_days,实际/9/avg_days)),2)
       - 结算金额：人月工时 × 单价
  3. 结果存储到核算结果表

## 3. 数据库设计
### 3.1 建表语句

```sql
CREATE TABLE `attendance_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_no` varchar(50) NOT NULL COMMENT '账号',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `check_date` date NOT NULL COMMENT '日期',
  `department` varchar(100) DEFAULT NULL COMMENT '部门',
  `position` varchar(100) DEFAULT NULL COMMENT '职务',
  `work_rule` varchar(100) DEFAULT NULL COMMENT '所属规则',
  `check_type` varchar(20) DEFAULT NULL COMMENT '打卡类型(上班/下班)',
  `scheduled_time` time DEFAULT NULL COMMENT '应打卡时间',
  `actual_time` time DEFAULT NULL COMMENT '实际打卡时间',
  `check_status` varchar(20) DEFAULT NULL COMMENT '打卡状态(正常/迟到/早退等)',
  `location` varchar(100) DEFAULT NULL COMMENT '打卡地点',
  `device_id` varchar(50) DEFAULT NULL COMMENT '打卡设备',
  `remark` text COMMENT '备注内容',
  `remark_image` varchar(255) DEFAULT NULL COMMENT '备注图片路径',
  `leave_application` text COMMENT '假勤申请内容',
  PRIMARY KEY (`id`),
  KEY `idx_staff_no` (`staff_no`),
  KEY `idx_check_date` (`check_date`),
  KEY `idx_actual_time` (`actual_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='打卡明细表';

CREATE TABLE `attendance_summary` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_no` varchar(50) NOT NULL COMMENT '账号',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `record_date` date NOT NULL COMMENT '时间',
  `department` varchar(100) DEFAULT NULL COMMENT '部门',
  `position` varchar(100) DEFAULT NULL COMMENT '职务',
  `work_rule` varchar(100) DEFAULT NULL COMMENT '所属规则',
  `shift` varchar(50) DEFAULT NULL COMMENT '班次',
  `earliest_check` time DEFAULT NULL COMMENT '最早打卡时间',
  `latest_check` time DEFAULT NULL COMMENT '最晚打卡时间',
  `check_count` int(11) DEFAULT NULL COMMENT '打卡次数(次)',
  `standard_hours` decimal(5,2) DEFAULT NULL COMMENT '标准工作时长(小时)',
  `actual_hours` decimal(5,2) DEFAULT NULL COMMENT '实际工作时长(小时)',
  `calculated_hours` decimal(5,2) DEFAULT NULL COMMENT '实际打卡按9小时算',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_staff_date` (`staff_no`,`record_date`),
  KEY `idx_record_date` (`record_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='考勤概况表';

CREATE TABLE `menus` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '菜单名称',
  `url` varchar(200) DEFAULT NULL COMMENT '菜单URL',
  `parent_id` int(11) DEFAULT NULL COMMENT '父菜单ID',
  `icon` varchar(50) DEFAULT NULL COMMENT '菜单图标',
  `order_num` int(11) DEFAULT '0' COMMENT '排序号',
  `permission` varchar(100) DEFAULT NULL COMMENT '权限标识',
  `menu_type` varchar(10) NOT NULL COMMENT '菜单类型(0:目录 1:菜单 2:按钮)',
  `status` int(11) DEFAULT '1' COMMENT '状态(0:禁用 1:启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`),
  CONSTRAINT `menus_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `menus` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '登录账号',
  `password` varchar(100) NOT NULL COMMENT '密码(加密存储)',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='系统用户表';

CREATE TABLE `role` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `menu_permission` text COMMENT '菜单权限JSON',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

CREATE TABLE `user_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `role_id` int(11) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

CREATE TABLE `settlement_result` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL COMMENT '人员ID',
  `month` date NOT NULL COMMENT '核算月份',
  `standard_hours` decimal(10,2) DEFAULT NULL COMMENT '标准工作时长(小时)',
  `calculated_hours` decimal(10,2) DEFAULT NULL COMMENT '实际打卡按9小时算',
  `work_days` decimal(10,2) DEFAULT NULL COMMENT '人月工时',
  `amount` decimal(10,2) DEFAULT NULL COMMENT '结算金额',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_staff_month` (`staff_id`,`month`),
  KEY `idx_month` (`month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='核算结果表';

CREATE TABLE `staff_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_no` varchar(50) NOT NULL COMMENT '工号',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `vendor_id` int(11) NOT NULL COMMENT '所属厂商ID',
  `project_id` int(11) DEFAULT NULL COMMENT '所属项目ID',
  `role_id` int(11) DEFAULT NULL COMMENT '角色ID',
  `experience_years` decimal(10,1) DEFAULT NULL COMMENT '工作经验年限',
  `unit_price` decimal(10,2) NOT NULL COMMENT '单价(元/人月)',
  `staff_level` int(11) DEFAULT NULL COMMENT '人员级别',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态(1-在职 0-离职)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_staff_no` (`staff_no`),
  KEY `idx_vendor_id` (`vendor_id`)
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4 COMMENT='人员信息表';
CREATE TABLE `vendor` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '厂商名称',
  `avg_work_days` decimal(5,2) NOT NULL DEFAULT '20.83' COMMENT '平均计薪工作日',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COMMENT='厂商配置表';
```
### 3.2 表结构说明
1. 用户相关表：
   - `user`: 用户基本信息
   - `role`: 角色定义
   - `user_role`: 用户角色关联
   - `menus`: 菜单权限

2. 考勤相关表：
   - `attendance_summary`: 考勤概况
   - `attendance_detail`: 打卡明细

3. 结算相关表：
   - `staff_info`: 人员信息
   - `vendor`: 厂商配置
   - `settlement_result`: 核算结果

## 4. 界面原型描述

### 4.1 登录界面
- 用户名/密码输入框
- 图形验证码区域
- 登录按钮

### 4.2 系统首页
- 顶部栏：系统logo、用户信息、时间显示
- 左侧菜单：树形结构，根据权限显示
- 右侧内容区：默认欢迎信息

### 4.3 数据导入界面
- Excel上传区域
- 导入进度显示
- 异常提示区域

### 4.4 核算界面
- 月份选择器
- 厂商筛选下拉框
- 核算结果表格
- 导出按钮