<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.timetrex.system.mapper.AttendanceSummaryMapper">

    <resultMap type="AttendanceSummary" id="AttendanceSummaryResult">
        <result property="id"    column="id"    />
        <result property="staffNo"    column="staff_no"    />
        <result property="name"    column="name"    />
        <result property="recordDate"    column="record_date"    />
        <result property="department"    column="department"    />
        <result property="position"    column="position"    />
        <result property="workRule"    column="work_rule"    />
        <result property="shift"    column="shift"    />
        <result property="earliestCheck"    column="earliest_check"    />
        <result property="latestCheck"    column="latest_check"    />
        <result property="checkCount"    column="check_count"    />
        <result property="standardHours"    column="standard_hours"    />
        <result property="actualHours"    column="actual_hours"    />
        <result property="calculatedHours"    column="calculated_hours"    />
        <result property="dayOfWeek"    column="day_of_week"    />
    </resultMap>

    <sql id="selectAttendanceSummaryVo">
        select id, staff_no, name, record_date, department, position, work_rule, shift, earliest_check, latest_check, check_count, standard_hours, actual_hours, calculated_hours, day_of_week from attendance_summary
    </sql>

    <select id="selectAttendanceSummaryList" parameterType="AttendanceSummary" resultMap="AttendanceSummaryResult">
        <include refid="selectAttendanceSummaryVo"/>
        <where>
            <if test="staffNo != null  and staffNo != ''"> and staff_no = #{staffNo}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="recordDate != null "> and record_date = #{recordDate}</if>
            <if test="department != null  and department != ''"> and department = #{department}</if>
            <if test="position != null  and position != ''"> and position = #{position}</if>
            <if test="workRule != null  and workRule != ''"> and work_rule = #{workRule}</if>
            <if test="shift != null  and shift != ''"> and shift = #{shift}</if>
            <if test="dayOfWeek != null  and dayOfWeek != ''"> and day_of_week = #{dayOfWeek}</if>
        </where>
    </select>

    <select id="selectAttendanceSummaryById" parameterType="Long" resultMap="AttendanceSummaryResult">
        <include refid="selectAttendanceSummaryVo"/>
        where id = #{id}
    </select>

    <select id="selectAttendanceSummaryByMonthAndStaffNo" resultMap="AttendanceSummaryResult">
        <include refid="selectAttendanceSummaryVo"/>
        where DATE_FORMAT(record_date, '%Y-%m') = DATE_FORMAT(#{month}, '%Y-%m')
        and staff_no = #{staffNo}
    </select>

    <select id="selectAttendanceSummaryByMonth" parameterType="java.time.LocalDate" resultMap="AttendanceSummaryResult">
        <include refid="selectAttendanceSummaryVo"/>
        where DATE_FORMAT(record_date, '%Y-%m') = DATE_FORMAT(#{month}, '%Y-%m')
    </select>

    <insert id="insertAttendanceSummary" parameterType="AttendanceSummary" useGeneratedKeys="true" keyProperty="id">
        insert into attendance_summary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="staffNo != null">staff_no,</if>
            <if test="name != null">name,</if>
            <if test="recordDate != null">record_date,</if>
            <if test="department != null">department,</if>
            <if test="position != null">position,</if>
            <if test="workRule != null">work_rule,</if>
            <if test="shift != null">shift,</if>
            <if test="earliestCheck != null">earliest_check,</if>
            <if test="latestCheck != null">latest_check,</if>
            <if test="checkCount != null">check_count,</if>
            <if test="standardHours != null">standard_hours,</if>
            <if test="actualHours != null">actual_hours,</if>
            <if test="calculatedHours != null">calculated_hours,</if>
            <if test="dayOfWeek != null">day_of_week,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="staffNo != null">#{staffNo},</if>
            <if test="name != null">#{name},</if>
            <if test="recordDate != null">#{recordDate},</if>
            <if test="department != null">#{department},</if>
            <if test="position != null">#{position},</if>
            <if test="workRule != null">#{workRule},</if>
            <if test="shift != null">#{shift},</if>
            <if test="earliestCheck != null">#{earliestCheck},</if>
            <if test="latestCheck != null">#{latestCheck},</if>
            <if test="checkCount != null">#{checkCount},</if>
            <if test="standardHours != null">#{standardHours},</if>
            <if test="actualHours != null">#{actualHours},</if>
            <if test="calculatedHours != null">#{calculatedHours},</if>
            <if test="dayOfWeek != null">#{dayOfWeek},</if>
         </trim>
    </insert>

    <insert id="batchInsertAttendanceSummary" parameterType="java.util.List">
        insert into attendance_summary (
            staff_no, name, record_date, department, position, work_rule, shift,
            earliest_check, latest_check, check_count, standard_hours, actual_hours,
            calculated_hours, day_of_week
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.staffNo}, #{item.name}, #{item.recordDate}, #{item.department},
                #{item.position}, #{item.workRule}, #{item.shift}, #{item.earliestCheck},
                #{item.latestCheck}, #{item.checkCount}, #{item.standardHours},
                #{item.actualHours}, #{item.calculatedHours}, #{item.dayOfWeek}
            )
        </foreach>
    </insert>

    <update id="updateAttendanceSummary" parameterType="AttendanceSummary">
        update attendance_summary
        <trim prefix="SET" suffixOverrides=",">
            <if test="staffNo != null">staff_no = #{staffNo},</if>
            <if test="name != null">name = #{name},</if>
            <if test="recordDate != null">record_date = #{recordDate},</if>
            <if test="department != null">department = #{department},</if>
            <if test="position != null">position = #{position},</if>
            <if test="workRule != null">work_rule = #{workRule},</if>
            <if test="shift != null">shift = #{shift},</if>
            <if test="earliestCheck != null">earliest_check = #{earliestCheck},</if>
            <if test="latestCheck != null">latest_check = #{latestCheck},</if>
            <if test="checkCount != null">check_count = #{checkCount},</if>
            <if test="standardHours != null">standard_hours = #{standardHours},</if>
            <if test="actualHours != null">actual_hours = #{actualHours},</if>
            <if test="calculatedHours != null">calculated_hours = #{calculatedHours},</if>
            <if test="dayOfWeek != null">day_of_week = #{dayOfWeek},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAttendanceSummaryById" parameterType="Long">
        delete from attendance_summary where id = #{id}
    </delete>

    <delete id="deleteAttendanceSummaryByIds" parameterType="String">
        delete from attendance_summary where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
