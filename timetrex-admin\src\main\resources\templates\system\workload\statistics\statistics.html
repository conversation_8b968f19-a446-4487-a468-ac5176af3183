<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('工作量统计')" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
    <div class="container-fluid">
        <div class="animated fadeInRight">
            <div class="row">
                <div class="col-sm-12">
                    <div class="ibox">
                        <div class="ibox-title">
                            <h5>工作量统计</h5>
                        </div>
                        <div class="ibox-content">
                            <!-- 查询条件 -->
                            <div class="row">
                                <div class="col-sm-12">
                                    <form class="form-inline" id="formSearch">
                                        <div class="form-group">
                                            <label>厂商：</label>
                                            <select name="vendorId" class="form-control selectpicker" data-live-search="true" title="全部厂商">
                                                <option value="">全部厂商</option>
                                                <option th:each="vendor : ${vendorList}" th:value="${vendor.id}" th:text="${vendor.name}"></option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>年份：</label>
                                            <select name="recordYear" class="form-control">
                                                <option value="">全部年份</option>
                                                <option th:each="year : ${#numbers.sequence(2024, currentYear + 1)}" 
                                                        th:value="${year}" th:text="${year}" th:selected="${year == currentYear}"></option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>月份：</label>
                                            <select name="recordMonth" class="form-control">
                                                <option value="">全部月份</option>
                                                <option th:each="month : ${#numbers.sequence(1, 12)}" 
                                                        th:value="${month}" th:text="${month + '月'}"></option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <button type="button" class="btn btn-primary" onclick="loadStatistics()">
                                                <i class="fa fa-search"></i> 查询
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            
                            <!-- 统计卡片 -->
                            <div class="row" style="margin-top: 20px;">
                                <div class="col-lg-3 col-md-6">
                                    <div class="ibox">
                                        <div class="ibox-title">
                                            <span class="label label-success pull-right">总计</span>
                                            <h5>记录数量</h5>
                                        </div>
                                        <div class="ibox-content">
                                            <h1 class="no-margins" id="totalRecords">0</h1>
                                            <small>工作量登记记录</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <div class="ibox">
                                        <div class="ibox-title">
                                            <span class="label label-info pull-right">累计</span>
                                            <h5>工作天数</h5>
                                        </div>
                                        <div class="ibox-content">
                                            <h1 class="no-margins" id="totalWorkDays">0</h1>
                                            <small>总工作天数</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <div class="ibox">
                                        <div class="ibox-title">
                                            <span class="label label-warning pull-right">累计</span>
                                            <h5>人月数</h5>
                                        </div>
                                        <div class="ibox-content">
                                            <h1 class="no-margins" id="totalPersonMonths">0</h1>
                                            <small>总人月数</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <div class="ibox">
                                        <div class="ibox-title">
                                            <span class="label label-danger pull-right">金额</span>
                                            <h5>结算金额</h5>
                                        </div>
                                        <div class="ibox-content">
                                            <h1 class="no-margins" id="totalAmount">¥0</h1>
                                            <small>总结算金额</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 状态分布 -->
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="ibox">
                                        <div class="ibox-title">
                                            <h5>状态分布</h5>
                                        </div>
                                        <div class="ibox-content">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>状态</th>
                                                        <th>数量</th>
                                                        <th>占比</th>
                                                        <th>进度条</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td><span class="label label-default">草稿</span></td>
                                                        <td id="draftCount">0</td>
                                                        <td id="draftPercent">0%</td>
                                                        <td>
                                                            <div class="progress progress-mini">
                                                                <div class="progress-bar" id="draftProgress" style="width: 0%;"></div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><span class="label label-info">已提交</span></td>
                                                        <td id="submittedCount">0</td>
                                                        <td id="submittedPercent">0%</td>
                                                        <td>
                                                            <div class="progress progress-mini">
                                                                <div class="progress-bar progress-bar-info" id="submittedProgress" style="width: 0%;"></div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><span class="label label-warning">已计算</span></td>
                                                        <td id="calculatedCount">0</td>
                                                        <td id="calculatedPercent">0%</td>
                                                        <td>
                                                            <div class="progress progress-mini">
                                                                <div class="progress-bar progress-bar-warning" id="calculatedProgress" style="width: 0%;"></div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td><span class="label label-success">已审批</span></td>
                                                        <td id="approvedCount">0</td>
                                                        <td id="approvedPercent">0%</td>
                                                        <td>
                                                            <div class="progress progress-mini">
                                                                <div class="progress-bar progress-bar-success" id="approvedProgress" style="width: 0%;"></div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-select-js" />
    <script th:inline="javascript">
        var ctx = [[@{/}]];
        
        $(function() {
            $('.selectpicker').selectpicker();
            loadStatistics();
        });
        
        function loadStatistics() {
            var formData = $('#formSearch').serialize();
            
            $.ajax({
                url: ctx + "system/workload/statistics/data",
                type: "POST",
                data: formData,
                success: function(result) {
                    if (result.code == 0) {
                        updateStatistics(result.data);
                    } else {
                        $.modal.alertError("获取统计数据失败：" + result.msg);
                    }
                },
                error: function() {
                    $.modal.alertError("获取统计数据失败");
                }
            });
        }
        
        function updateStatistics(data) {
            // 更新统计卡片
            $('#totalRecords').text(data.totalRecords || 0);
            $('#totalWorkDays').text(parseFloat(data.totalWorkDays || 0).toFixed(1));
            $('#totalPersonMonths').text(parseFloat(data.totalPersonMonths || 0).toFixed(2));
            $('#totalAmount').text('¥' + parseFloat(data.totalAmount || 0).toLocaleString('zh-CN', {minimumFractionDigits: 2}));
            
            // 更新状态分布
            var total = data.totalRecords || 0;
            updateStatusRow('draft', data.draftCount || 0, total);
            updateStatusRow('submitted', data.submittedCount || 0, total);
            updateStatusRow('calculated', data.calculatedCount || 0, total);
            updateStatusRow('approved', data.approvedCount || 0, total);
        }
        
        function updateStatusRow(status, count, total) {
            var percent = total > 0 ? Math.round((count / total) * 100) : 0;
            $('#' + status + 'Count').text(count);
            $('#' + status + 'Percent').text(percent + '%');
            $('#' + status + 'Progress').css('width', percent + '%');
        }
    </script>
</body>
</html>
