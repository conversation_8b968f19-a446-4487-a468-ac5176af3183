# 外包管理系统功能说明文档

## 系统概述
- 系统名称：外包管理系统
- 主要功能模块：厂商管理、人员管理
- 技术实现：纯HTML/CSS/JavaScript + SheetJS库(Excel导出)

## 厂商管理功能

### 查询厂商
- **查询字段**:
  - 厂商名称(模糊匹配)
- **查询结果展示**:
  - 表格列: 序号、厂商名称、平均计薪工作日数、操作列
  - 每行操作按钮: 编辑、删除
- **交互流程**:
  - 点击"查询"按钮: 执行查询并刷新表格
  - 点击"编辑"按钮: 弹出编辑厂商表单
  - 点击"删除"按钮: 弹出确认对话框

### 新增厂商
- **表单字段**:
  - 厂商名称(必填)
  - 平均计薪工作日数(必填，数字)
- **交互流程**:
  - 点击"新增厂商"按钮: 弹出空表单
  - 提交后: 刷新厂商列表

### 编辑厂商
- 复用新增厂商表单
- 自动填充选中厂商的现有数据

### 删除厂商
- 弹出确认对话框
- 确认后从列表中移除

### 导出Excel
- 导出当前查询结果
- 文件名为"厂商数据.xlsx"
- 包含字段: 厂商名称、平均计薪工作日数

## 人员管理功能

### 查询人员
- **查询字段**:
  - 人员姓名(模糊匹配)
  - 所属厂商(下拉选择)
  - 状态(下拉选择: 在职/离职)
- **查询结果展示**:
  - 表格列: 序号、业务条线、所属项目、姓名、角色、级别、工作经验、所属厂商、单价、状态、操作列
  - 每行操作按钮: 编辑、删除
- **交互流程**:
  - 点击"查询"按钮: 执行查询并刷新表格
  - 点击"重置"按钮: 清空查询条件
  - 点击"编辑"按钮: 弹出编辑表单
  - 点击"删除"按钮: 弹出确认对话框

### 新增人员
- **表单字段**:
  - 姓名(必填)
  - 业务条线(必填，下拉选择)
  - 所属项目(必填，下拉选择)
  - 角色(必填，下拉选择)
  - 级别(必填，下拉选择)
  - 工作经验(年)(必填，数字)
  - 所属厂商(必填，下拉选择)
  - 单价(人/月)(必填，数字)
- **交互流程**:
  - 点击"新增人员"按钮: 弹出空表单
  - 提交后: 刷新人员列表

### 编辑人员
- 复用新增人员表单
- 自动填充选中人员的现有数据

### 删除人员
- 弹出确认对话框
- 确认后从列表中移除

### 导出Excel
- 导出当前查询结果
- 文件名为"人员数据.xlsx"
- 包含字段: 业务条线、所属项目、姓名、角色、级别、工作经验、所属厂商、单价、状态

## 系统枚举值

### 业务条线
- 出行承保
- 商险承保  
- 商险理赔
- 理赔服务
- 销管
- 互联网
- 业财一体化
- 客服中心
- 再保
- 公共
- 前端
- 运维
- 测试
- 数据部

### 角色
- 开发
- 测试
- 运维

### 级别
- 高级-3级
- 高级-2级
- 高级-1级
- 中级-3级
- 中级-2级
- 中级-1级
- 初级-3级
- 初级-2级
- 初级-1级

### 状态
- 有效(1)
- 无效(0)

## 系统界面布局

### 1. 整体布局结构
- **顶部导航栏**
  - 显示系统名称"外包管理系统"
  - 右侧显示当前用户信息
  - 采用蓝色背景(#0078D7)和白色文字

- **主体容器**
  - 采用flex布局，分为左侧菜单和主内容区
  - 高度100vh，确保填满整个视口

### 2. 左侧菜单栏
- 宽度200px，白色背景
- 包含可折叠的菜单项：
  - "外包管理"主菜单（可展开/折叠）
    - "厂商管理"子菜单项
    - "人员管理"子菜单项
- 菜单项有悬停效果（颜色变为#0078D7）
- 当前选中菜单项有active样式（蓝色背景）

### 3. 主内容区
- **查询区域**
  - 顶部包含查询表单（可按名称、厂商、状态等条件查询）
  - 右侧有"新增"按钮（+ 新增厂商/人员）
  
- **数据表格**
  - 显示厂商/人员列表
  - 表格有斑马纹效果（隔行不同背景色）
  - 每行包含"编辑"和"删除"操作按钮

- **分页和导出区域**
  - 显示总条数信息
  - 分页按钮（当前页有active样式）
  - "导出Excel"按钮

### 4. 弹出模态框
- **厂商管理模态框**
  - 包含厂商名称和平均计薪工作日数字段
  - 底部有"取消"和"保存"按钮

- **人员管理模态框**
  - 包含多个表单字段（姓名、业务条线、所属项目等）
  - 采用flex布局，字段分多列显示
  - 底部有"取消"和"保存"按钮

### 5. 交互功能
- 所有表单都有必填验证
- 删除操作有确认对话框
- 表格数据可通过SheetJS库导出Excel
- 验证码图片可点击刷新

### 6. 样式特点
- 采用简洁的企业风格，主色调为蓝色(#0078D7)
- 按钮有悬停和点击效果
- 表单元素有统一的边框和圆角样式
- 响应式设计，适应不同屏幕尺寸
