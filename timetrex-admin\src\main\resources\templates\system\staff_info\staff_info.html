<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('人员信息列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>工号：</label>
                                <input type="text" name="staffNo"/>
                            </li>
                            <li>
                                <label>姓名：</label>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <label>所属厂商：</label>
                                <select name="vendorId" th:with="type=${@dict.getType('sys_outsource_factory')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>所属项目：</label>
                                <select name="projectId" th:with="type=${@dict.getType('sys_outsource_project')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>角色：</label>
                                <select name="roleId" th:with="type=${@dict.getType('sys_outsource_role')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>工作经验年限：</label>
                                <input type="text" name="experienceYears"/>
                            </li>
                            <li>
                                <label>单价(元/人月)：</label>
                                <input type="text" name="unitPrice"/>
                            </li>
                            <li>
                                <label>人员级别：</label>
                                <select name="staffLevel" th:with="type=${@dict.getType('sys_outsource_level')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:staff_info:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:staff_info:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:staff_info:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:staff_info:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('system:staff_info:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:staff_info:remove')}]];
        var vendorIdDatas = [[${@dict.getType('sys_outsource_factory')}]];
        var projectIdDatas = [[${@dict.getType('sys_outsource_project')}]];
        var roleIdDatas = [[${@dict.getType('sys_outsource_role')}]];
        var staffLevelDatas = [[${@dict.getType('sys_outsource_level')}]];
        var prefix = ctx + "system/staff_info";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "人员信息",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'staffNo',
                    title: '工号'
                },
                {
                    field: 'name',
                    title: '姓名'
                },
                {
                    field: 'vendorId',
                    title: '所属厂商',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(vendorIdDatas, value);
                    }
                },
                {
                    field: 'projectId',
                    title: '所属项目',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(projectIdDatas, value);
                    }
                },
                {
                    field: 'roleId',
                    title: '角色',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(roleIdDatas, value);
                    }
                },
                {
                    field: 'experienceYears',
                    title: '工作经验年限'
                },
                {
                    field: 'unitPrice',
                    title: '单价(元/人月)'
                },
                {
                    field: 'staffLevel',
                    title: '人员级别',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(staffLevelDatas, value);
                    }
                },
                {
                    field: 'status',
                    title: '状态(1-在职 0-离职)'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>