package com.timetrex.system.mapper;

import java.time.LocalDate;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.timetrex.system.domain.SettlementResult;

/**
 * 核算结果Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
public interface SettlementResultMapper
{
    /**
     * 查询核算结果
     *
     * @param id 核算结果主键
     * @return 核算结果
     */
    public SettlementResult selectSettlementResultById(Long id);

    /**
     * 查询核算结果列表
     *
     * @param settlementResult 核算结果
     * @return 核算结果集合
     */
    public List<SettlementResult> selectSettlementResultList(SettlementResult settlementResult);

    /**
     * 根据月份和厂商ID查询核算结果
     *
     * @param month 月份
     * @param vendorId 厂商ID
     * @return 核算结果集合
     */
    public List<SettlementResult> selectSettlementResultByMonthAndVendor(@Param("month") LocalDate month, @Param("vendorId") Long vendorId);

    /**
     * 新增核算结果
     *
     * @param settlementResult 核算结果
     * @return 结果
     */
    public int insertSettlementResult(SettlementResult settlementResult);

    /**
     * 批量新增核算结果
     *
     * @param settlementResultList 核算结果列表
     * @return 结果
     */
    public int batchInsertSettlementResult(List<SettlementResult> settlementResultList);

    /**
     * 修改核算结果
     *
     * @param settlementResult 核算结果
     * @return 结果
     */
    public int updateSettlementResult(SettlementResult settlementResult);

    /**
     * 删除核算结果
     *
     * @param id 核算结果主键
     * @return 结果
     */
    public int deleteSettlementResultById(Long id);

    /**
     * 批量删除核算结果
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSettlementResultByIds(String[] ids);

    /**
     * 根据月份和厂商ID删除核算结果
     *
     * @param month 月份
     * @param vendorId 厂商ID
     * @return 结果
     */
    public int deleteSettlementResultByMonthAndVendor(@Param("month") LocalDate month, @Param("vendorId") Long vendorId);
}
