package com.timetrex.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.timetrex.system.mapper.StaffInfoMapper;
import com.timetrex.system.domain.StaffInfo;
import com.timetrex.system.service.IStaffInfoService;
import com.timetrex.common.core.text.Convert;

/**
 * 人员信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-15
 */
@Service
public class StaffInfoServiceImpl implements IStaffInfoService 
{
    @Autowired
    private StaffInfoMapper staffInfoMapper;

    /**
     * 查询人员信息
     * 
     * @param id 人员信息主键
     * @return 人员信息
     */
    @Override
    public StaffInfo selectStaffInfoById(Long id)
    {
        return staffInfoMapper.selectStaffInfoById(id);
    }

    /**
     * 查询人员信息列表
     * 
     * @param staffInfo 人员信息
     * @return 人员信息
     */
    @Override
    public List<StaffInfo> selectStaffInfoList(StaffInfo staffInfo)
    {
        return staffInfoMapper.selectStaffInfoList(staffInfo);
    }

    /**
     * 新增人员信息
     * 
     * @param staffInfo 人员信息
     * @return 结果
     */
    @Override
    public int insertStaffInfo(StaffInfo staffInfo)
    {
        return staffInfoMapper.insertStaffInfo(staffInfo);
    }

    /**
     * 修改人员信息
     * 
     * @param staffInfo 人员信息
     * @return 结果
     */
    @Override
    public int updateStaffInfo(StaffInfo staffInfo)
    {
        return staffInfoMapper.updateStaffInfo(staffInfo);
    }

    /**
     * 批量删除人员信息
     * 
     * @param ids 需要删除的人员信息主键
     * @return 结果
     */
    @Override
    public int deleteStaffInfoByIds(String ids)
    {
        return staffInfoMapper.deleteStaffInfoByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除人员信息信息
     * 
     * @param id 人员信息主键
     * @return 结果
     */
    @Override
    public int deleteStaffInfoById(Long id)
    {
        return staffInfoMapper.deleteStaffInfoById(id);
    }
}
