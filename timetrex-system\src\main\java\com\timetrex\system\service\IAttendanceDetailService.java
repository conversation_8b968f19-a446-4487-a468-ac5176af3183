package com.timetrex.system.service;

import java.util.List;
import com.timetrex.system.domain.AttendanceDetail;

/**
 * 打卡详情Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-09
 */
public interface IAttendanceDetailService 
{
    /**
     * 查询打卡详情
     * 
     * @param id 打卡详情主键
     * @return 打卡详情
     */
    public AttendanceDetail selectAttendanceDetailById(Long id);

    /**
     * 查询打卡详情列表
     * 
     * @param attendanceDetail 打卡详情
     * @return 打卡详情集合
     */
    public List<AttendanceDetail> selectAttendanceDetailList(AttendanceDetail attendanceDetail);

    /**
     * 新增打卡详情
     * 
     * @param attendanceDetail 打卡详情
     * @return 结果
     */
    public int insertAttendanceDetail(AttendanceDetail attendanceDetail);
    
    /**
     * 批量新增打卡详情
     * 
     * @param attendanceDetailList 打卡详情列表
     * @return 结果
     */
    public int batchInsertAttendanceDetail(List<AttendanceDetail> attendanceDetailList);

    /**
     * 修改打卡详情
     * 
     * @param attendanceDetail 打卡详情
     * @return 结果
     */
    public int updateAttendanceDetail(AttendanceDetail attendanceDetail);

    /**
     * 批量删除打卡详情
     * 
     * @param ids 需要删除的打卡详情主键集合
     * @return 结果
     */
    public int deleteAttendanceDetailByIds(String ids);

    /**
     * 删除打卡详情信息
     * 
     * @param id 打卡详情主键
     * @return 结果
     */
    public int deleteAttendanceDetailById(Long id);
    
    /**
     * 检查设备ID是否被多人使用
     * 
     * @param deviceId 设备ID
     * @return 是否被多人使用
     */
    public boolean checkDeviceUsedByMultipleStaff(String deviceId);
}
