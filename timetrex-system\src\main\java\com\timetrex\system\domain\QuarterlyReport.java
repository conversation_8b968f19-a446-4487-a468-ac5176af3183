package com.timetrex.system.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.timetrex.common.annotation.Excel;
import com.timetrex.common.core.domain.BaseEntity;

/**
 * 季度归总报表对象
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
public class QuarterlyReport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 人员姓名 */
    @Excel(name = "人员姓名")
    private String staffName;

    /** 厂商ID */
    private Long vendorId;

    /** 厂商名称 */
    @Excel(name = "厂商名称")
    private String vendorName;

    /** 年份 */
    @Excel(name = "年份")
    private Integer year;

    /** 季度 */
    @Excel(name = "季度")
    private Integer quarter;

    /** 第一个月标准工作时长 */
    @Excel(name = "第一个月标准工作时长")
    private BigDecimal standardHours1;

    /** 第一个月实际打卡按9小时算 */
    @Excel(name = "第一个月实际打卡按9小时算")
    private BigDecimal calculatedHours1;

    /** 第一个月人月工时 */
    @Excel(name = "第一个月人月工时")
    private BigDecimal workDays1;

    /** 第一个月结算金额 */
    @Excel(name = "第一个月结算金额")
    private BigDecimal amount1;

    /** 第二个月标准工作时长 */
    @Excel(name = "第二个月标准工作时长")
    private BigDecimal standardHours2;

    /** 第二个月实际打卡按9小时算 */
    @Excel(name = "第二个月实际打卡按9小时算")
    private BigDecimal calculatedHours2;

    /** 第二个月人月工时 */
    @Excel(name = "第二个月人月工时")
    private BigDecimal workDays2;

    /** 第二个月结算金额 */
    @Excel(name = "第二个月结算金额")
    private BigDecimal amount2;

    /** 第三个月标准工作时长 */
    @Excel(name = "第三个月标准工作时长")
    private BigDecimal standardHours3;

    /** 第三个月实际打卡按9小时算 */
    @Excel(name = "第三个月实际打卡按9小时算")
    private BigDecimal calculatedHours3;

    /** 第三个月人月工时 */
    @Excel(name = "第三个月人月工时")
    private BigDecimal workDays3;

    /** 第三个月结算金额 */
    @Excel(name = "第三个月结算金额")
    private BigDecimal amount3;

    /** 季度标准工作时长总和 */
    @Excel(name = "季度标准工作时长总和")
    private BigDecimal totalStandardHours;

    /** 季度实际打卡按9小时算总和 */
    @Excel(name = "季度实际打卡按9小时算总和")
    private BigDecimal totalCalculatedHours;

    /** 季度人月工时总和 */
    @Excel(name = "季度人月工时总和")
    private BigDecimal totalWorkDays;

    /** 季度结算金额总和 */
    @Excel(name = "季度结算金额总和")
    private BigDecimal totalAmount;

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public Long getVendorId() {
        return vendorId;
    }

    public void setVendorId(Long vendorId) {
        this.vendorId = vendorId;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getQuarter() {
        return quarter;
    }

    public void setQuarter(Integer quarter) {
        this.quarter = quarter;
    }

    public BigDecimal getStandardHours1() {
        return standardHours1;
    }

    public void setStandardHours1(BigDecimal standardHours1) {
        this.standardHours1 = standardHours1;
    }

    public BigDecimal getCalculatedHours1() {
        return calculatedHours1;
    }

    public void setCalculatedHours1(BigDecimal calculatedHours1) {
        this.calculatedHours1 = calculatedHours1;
    }

    public BigDecimal getWorkDays1() {
        return workDays1;
    }

    public void setWorkDays1(BigDecimal workDays1) {
        this.workDays1 = workDays1;
    }

    public BigDecimal getAmount1() {
        return amount1;
    }

    public void setAmount1(BigDecimal amount1) {
        this.amount1 = amount1;
    }

    public BigDecimal getStandardHours2() {
        return standardHours2;
    }

    public void setStandardHours2(BigDecimal standardHours2) {
        this.standardHours2 = standardHours2;
    }

    public BigDecimal getCalculatedHours2() {
        return calculatedHours2;
    }

    public void setCalculatedHours2(BigDecimal calculatedHours2) {
        this.calculatedHours2 = calculatedHours2;
    }

    public BigDecimal getWorkDays2() {
        return workDays2;
    }

    public void setWorkDays2(BigDecimal workDays2) {
        this.workDays2 = workDays2;
    }

    public BigDecimal getAmount2() {
        return amount2;
    }

    public void setAmount2(BigDecimal amount2) {
        this.amount2 = amount2;
    }

    public BigDecimal getStandardHours3() {
        return standardHours3;
    }

    public void setStandardHours3(BigDecimal standardHours3) {
        this.standardHours3 = standardHours3;
    }

    public BigDecimal getCalculatedHours3() {
        return calculatedHours3;
    }

    public void setCalculatedHours3(BigDecimal calculatedHours3) {
        this.calculatedHours3 = calculatedHours3;
    }

    public BigDecimal getWorkDays3() {
        return workDays3;
    }

    public void setWorkDays3(BigDecimal workDays3) {
        this.workDays3 = workDays3;
    }

    public BigDecimal getAmount3() {
        return amount3;
    }

    public void setAmount3(BigDecimal amount3) {
        this.amount3 = amount3;
    }

    public BigDecimal getTotalStandardHours() {
        return totalStandardHours;
    }

    public void setTotalStandardHours(BigDecimal totalStandardHours) {
        this.totalStandardHours = totalStandardHours;
    }

    public BigDecimal getTotalCalculatedHours() {
        return totalCalculatedHours;
    }

    public void setTotalCalculatedHours(BigDecimal totalCalculatedHours) {
        this.totalCalculatedHours = totalCalculatedHours;
    }

    public BigDecimal getTotalWorkDays() {
        return totalWorkDays;
    }

    public void setTotalWorkDays(BigDecimal totalWorkDays) {
        this.totalWorkDays = totalWorkDays;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("staffName", getStaffName())
            .append("vendorId", getVendorId())
            .append("vendorName", getVendorName())
            .append("year", getYear())
            .append("quarter", getQuarter())
            .append("standardHours1", getStandardHours1())
            .append("calculatedHours1", getCalculatedHours1())
            .append("workDays1", getWorkDays1())
            .append("amount1", getAmount1())
            .append("standardHours2", getStandardHours2())
            .append("calculatedHours2", getCalculatedHours2())
            .append("workDays2", getWorkDays2())
            .append("amount2", getAmount2())
            .append("standardHours3", getStandardHours3())
            .append("calculatedHours3", getCalculatedHours3())
            .append("workDays3", getWorkDays3())
            .append("amount3", getAmount3())
            .append("totalStandardHours", getTotalStandardHours())
            .append("totalCalculatedHours", getTotalCalculatedHours())
            .append("totalWorkDays", getTotalWorkDays())
            .append("totalAmount", getTotalAmount())
            .toString();
    }
}
