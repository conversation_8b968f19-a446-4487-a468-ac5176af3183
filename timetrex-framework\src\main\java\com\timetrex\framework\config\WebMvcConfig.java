package com.timetrex.framework.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.timetrex.common.utils.YearMonthConverter;

/**
 * Web MVC 配置
 * 
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    /**
     * 添加自定义转换器
     */
    @Override
    public void addFormatters(FormatterRegistry registry) {
        // 添加年月转换器
        registry.addConverter(new YearMonthConverter());
    }
}
