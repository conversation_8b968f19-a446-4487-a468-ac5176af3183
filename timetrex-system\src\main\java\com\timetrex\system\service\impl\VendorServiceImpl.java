package com.timetrex.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.timetrex.system.mapper.VendorMapper;
import com.timetrex.system.domain.Vendor;
import com.timetrex.system.service.IVendorService;
import com.timetrex.common.core.text.Convert;

/**
 * 厂商配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-15
 */
@Service
public class VendorServiceImpl implements IVendorService 
{
    @Autowired
    private VendorMapper vendorMapper;

    /**
     * 查询厂商配置
     * 
     * @param id 厂商配置主键
     * @return 厂商配置
     */
    @Override
    public Vendor selectVendorById(Long id)
    {
        return vendorMapper.selectVendorById(id);
    }

    /**
     * 查询厂商配置列表
     * 
     * @param vendor 厂商配置
     * @return 厂商配置
     */
    @Override
    public List<Vendor> selectVendorList(Vendor vendor)
    {
        return vendorMapper.selectVendorList(vendor);
    }

    /**
     * 新增厂商配置
     * 
     * @param vendor 厂商配置
     * @return 结果
     */
    @Override
    public int insertVendor(Vendor vendor)
    {
        return vendorMapper.insertVendor(vendor);
    }

    /**
     * 修改厂商配置
     * 
     * @param vendor 厂商配置
     * @return 结果
     */
    @Override
    public int updateVendor(Vendor vendor)
    {
        return vendorMapper.updateVendor(vendor);
    }

    /**
     * 批量删除厂商配置
     * 
     * @param ids 需要删除的厂商配置主键
     * @return 结果
     */
    @Override
    public int deleteVendorByIds(String ids)
    {
        return vendorMapper.deleteVendorByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除厂商配置信息
     * 
     * @param id 厂商配置主键
     * @return 结果
     */
    @Override
    public int deleteVendorById(Long id)
    {
        return vendorMapper.deleteVendorById(id);
    }
}
