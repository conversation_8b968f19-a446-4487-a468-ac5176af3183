package com.timetrex.system.service.impl;


import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.timetrex.common.config.TimeTrexConfig;

import com.timetrex.common.core.text.Convert;

import com.timetrex.system.domain.AttendanceSummary;
import com.timetrex.system.domain.SettlementResult;
import com.timetrex.system.domain.StaffInfo;
import com.timetrex.system.domain.Vendor;
import com.timetrex.system.mapper.SettlementResultMapper;
import com.timetrex.system.service.IAttendanceSummaryService;
import com.timetrex.system.service.IEmailService;
import com.timetrex.system.service.ISettlementResultService;
import com.timetrex.system.service.IStaffInfoService;
import com.timetrex.system.service.IVendorService;

/**
 * 核算结果Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Service
public class SettlementResultServiceImpl implements ISettlementResultService
{
    private static final Logger log = LoggerFactory.getLogger(SettlementResultServiceImpl.class);

    @Autowired
    private SettlementResultMapper settlementResultMapper;

    @Autowired
    private IAttendanceSummaryService attendanceSummaryService;

    @Autowired
    private IStaffInfoService staffInfoService;

    @Autowired
    private IVendorService vendorService;

    @Autowired
    private IEmailService emailService;

    /**
     * 查询核算结果
     *
     * @param id 核算结果主键
     * @return 核算结果
     */
    @Override
    public SettlementResult selectSettlementResultById(Long id)
    {
        return settlementResultMapper.selectSettlementResultById(id);
    }

    /**
     * 查询核算结果列表
     *
     * @param settlementResult 核算结果
     * @return 核算结果
     */
    @Override
    public List<SettlementResult> selectSettlementResultList(SettlementResult settlementResult)
    {
        return settlementResultMapper.selectSettlementResultList(settlementResult);
    }

    /**
     * 根据月份和厂商ID查询核算结果
     *
     * @param month 月份
     * @param vendorId 厂商ID
     * @return 核算结果集合
     */
    @Override
    public List<SettlementResult> selectSettlementResultByMonthAndVendor(LocalDate month, Long vendorId)
    {
        return settlementResultMapper.selectSettlementResultByMonthAndVendor(month, vendorId);
    }

    /**
     * 新增核算结果
     *
     * @param settlementResult 核算结果
     * @return 结果
     */
    @Override
    public int insertSettlementResult(SettlementResult settlementResult)
    {
        return settlementResultMapper.insertSettlementResult(settlementResult);
    }

    /**
     * 批量新增核算结果
     *
     * @param settlementResultList 核算结果列表
     * @return 结果
     */
    @Override
    public int batchInsertSettlementResult(List<SettlementResult> settlementResultList)
    {
        return settlementResultMapper.batchInsertSettlementResult(settlementResultList);
    }

    /**
     * 修改核算结果
     *
     * @param settlementResult 核算结果
     * @return 结果
     */
    @Override
    public int updateSettlementResult(SettlementResult settlementResult)
    {
        return settlementResultMapper.updateSettlementResult(settlementResult);
    }

    /**
     * 批量删除核算结果
     *
     * @param ids 需要删除的核算结果主键
     * @return 结果
     */
    @Override
    public int deleteSettlementResultByIds(String ids)
    {
        return settlementResultMapper.deleteSettlementResultByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除核算结果信息
     *
     * @param id 核算结果主键
     * @return 结果
     */
    @Override
    public int deleteSettlementResultById(Long id)
    {
        return settlementResultMapper.deleteSettlementResultById(id);
    }

    /**
     * 根据月份和厂商ID删除核算结果
     *
     * @param month 月份
     * @param vendorId 厂商ID
     * @return 结果
     */
    @Override
    public int deleteSettlementResultByMonthAndVendor(LocalDate month, Long vendorId)
    {
        return settlementResultMapper.deleteSettlementResultByMonthAndVendor(month, vendorId);
    }

    /**
     * 执行考勤核算
     *
     * @param month 月份
     * @param vendorId 厂商ID
     * @return 核算结果集合
     */
    @Override
    @Transactional
    public List<SettlementResult> calculateSettlement(LocalDate month, Long vendorId)
    {
        try {
            log.info("开始执行考勤核算，月份：{}，厂商ID：{}", month, vendorId);

            // 1. 删除已有的核算结果
            try {
                deleteSettlementResultByMonthAndVendor(month, vendorId);
                log.info("已删除已有的核算结果");
            } catch (Exception e) {
                log.error("删除已有核算结果失败", e);
                throw new RuntimeException("删除已有核算结果失败：" + e.getMessage(), e);
            }

            // 2. 获取厂商信息
            Vendor vendor = vendorService.selectVendorById(vendorId);
            if (vendor == null) {
                log.error("厂商不存在，厂商ID：{}", vendorId);
                throw new RuntimeException("厂商不存在，厂商ID：" + vendorId);
            }
            log.info("获取到厂商信息：{}", vendor.getName());

            // 3. 获取该厂商下的所有员工
            StaffInfo staffInfoQuery = new StaffInfo();
            staffInfoQuery.setVendorId(vendorId);
            staffInfoQuery.setStatus(1); // 在职状态
            List<StaffInfo> staffList = staffInfoService.selectStaffInfoList(staffInfoQuery);
            if (staffList.isEmpty()) {
                log.warn("该厂商下没有在职员工，厂商ID：{}", vendorId);
                return new ArrayList<>();
            }
            log.info("获取到厂商下的员工数量：{}", staffList.size());

            // 4. 获取该月份的考勤数据
            List<AttendanceSummary> attendanceSummaryList = attendanceSummaryService.selectAttendanceSummaryByMonth(month);
            if (attendanceSummaryList.isEmpty()) {
                log.warn("该月份没有考勤数据，月份：{}", month);
                return new ArrayList<>();
            }
            log.info("获取到考勤数据数量：{}", attendanceSummaryList.size());

            // 5. 按员工编号分组考勤数据
            Map<String, List<AttendanceSummary>> attendanceByStaffNo = attendanceSummaryList.stream()
                    .collect(Collectors.groupingBy(AttendanceSummary::getStaffNo));
            log.info("按员工编号分组后的考勤数据组数：{}", attendanceByStaffNo.size());

            // 6. 计算每个员工的核算结果
            List<SettlementResult> resultList = new ArrayList<>();
            for (StaffInfo staff : staffList) {
                try {
                    List<AttendanceSummary> staffAttendance = attendanceByStaffNo.get(staff.getStaffNo());
                    if (staffAttendance == null || staffAttendance.isEmpty()) {
                        log.warn("员工没有考勤记录，跳过计算，员工：{}，编号：{}", staff.getName(), staff.getStaffNo());
                        continue; // 该员工没有考勤记录，跳过
                    }
                    log.debug("开始计算员工核算结果，员工：{}，编号：{}", staff.getName(), staff.getStaffNo());

                    // 计算标准工作时长总和（排除周六日）
                    BigDecimal totalStandardHours = staffAttendance.stream()
                            .filter(a -> {
                                // 排除周六日
                                String dayOfWeek = a.getDayOfWeek();
                                return dayOfWeek != null && !dayOfWeek.equals("星期六") && !dayOfWeek.equals("星期日")
                                    && a.getStandardHours() != null;
                            })
                            .map(AttendanceSummary::getStandardHours)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    log.debug("员工 {} 标准工时总和（排除周六日）: {}", staff.getName(), totalStandardHours);

                    // 计算实际打卡按9小时算的总和（排除周六日和calculatedHours为空的情况）
                    BigDecimal totalCalculatedHours = staffAttendance.stream()
                            .filter(a -> {
                                // 排除周六日
                                String dayOfWeek = a.getDayOfWeek();
                                return dayOfWeek != null && !dayOfWeek.equals("星期六") && !dayOfWeek.equals("星期日")
                                    && a.getCalculatedHours() != null;
                            })
                            .map(AttendanceSummary::getCalculatedHours) // 直接使用已有的calculatedHours字段
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    log.debug("员工 {} 实际打卡按9小时算总和（排除周六日）: {}", staff.getName(), totalCalculatedHours);

                    // 计算人月工时 
                    BigDecimal workDays;
                    if (totalCalculatedHours.compareTo(BigDecimal.ZERO) == 0) {
                        workDays = BigDecimal.ZERO;
                    } else if (totalCalculatedHours.compareTo(totalStandardHours) > 0) {
                        workDays = totalStandardHours.divide(new BigDecimal("9"), 2, RoundingMode.HALF_UP)
                                .divide(vendor.getAvgWorkDays(), 2, RoundingMode.HALF_UP);
                    } else {
                        workDays = totalCalculatedHours.divide(new BigDecimal("9"), 2, RoundingMode.HALF_UP)
                                .divide(vendor.getAvgWorkDays(), 2, RoundingMode.HALF_UP);
                    }

                    // 计算结算金额
                    BigDecimal amount = workDays.multiply(staff.getUnitPrice()).setScale(2, RoundingMode.HALF_UP);

                    // 创建核算结果对象
                    SettlementResult result = new SettlementResult();
                    result.setStaffName(staff.getName());
                    result.setVendorId(vendorId);
                    result.setMonth(month);
                    result.setStandardHours(totalStandardHours);
                    result.setCalculatedHours(totalCalculatedHours);
                    result.setWorkDays(workDays);
                    result.setAmount(amount);

                    resultList.add(result);
                    log.debug("员工核算结果计算完成，员工：{}，编号：{}，结算金额：{}", staff.getName(), staff.getStaffNo(), amount);
                } catch (Exception e) {
                    log.error("计算员工核算结果失败，员工：{}，编号：{}", staff.getName(), staff.getStaffNo(), e);
                    // 继续处理下一个员工，不中断整个流程
                }
            }

            // 7. 批量保存核算结果
            if (!resultList.isEmpty()) {
                try {
                    batchInsertSettlementResult(resultList);
                    log.info("批量保存核算结果成功，数量：{}", resultList.size());
                } catch (Exception e) {
                    log.error("批量保存核算结果失败", e);
                    throw new RuntimeException("批量保存核算结果失败：" + e.getMessage(), e);
                }
            } else {
                log.warn("没有核算结果需要保存");
            }

            log.info("考勤核算执行完成，月份：{}，厂商ID：{}，结果数量：{}", month, vendorId, resultList.size());
            return resultList;
        } catch (Exception e) {
            log.error("考勤核算执行失败，月份：{}，厂商ID：{}", month, vendorId, e);
            throw new RuntimeException("考勤核算执行失败：" + e.getMessage(), e);
        }
    }

    /**
     * 导出并发送核算结果
     *
     * @param month 月份
     * @param vendorId 厂商ID
     * @param email 接收邮箱
     * @return 结果
     */
    @Override
    public boolean exportAndSendSettlementResult(LocalDate month, Long vendorId, String email)
    {
        try {
            log.info("开始导出并发送核算结果，月份：{}，厂商ID：{}，接收邮箱：{}", month, vendorId, email);

            // 1. 查询核算结果
            List<SettlementResult> resultList;
            try {
                resultList = selectSettlementResultByMonthAndVendor(month, vendorId);
                if (resultList == null || resultList.isEmpty()) {
                    log.error("未找到核算结果数据，月份：{}，厂商ID：{}", month, vendorId);
                    return false;
                }
                log.info("查询到核算结果数据，数量：{}", resultList.size());
            } catch (Exception e) {
                log.error("查询核算结果数据失败，月份：{}，厂商ID：{}", month, vendorId, e);
                throw new RuntimeException("查询核算结果数据失败：" + e.getMessage(), e);
            }

            // 2. 导出Excel
            String monthStr = month.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            String vendorName = resultList.get(0).getVendorName();
            String fileName = vendorName + "_" + monthStr + "_核算结果.xlsx";
            String filePath = TimeTrexConfig.getProfile() + "/" + fileName;
            log.info("准备导出Excel文件：{}", filePath);

            // 使用简单的方式导出Excel
            try {
                // 创建Excel文件
                Workbook workbook = new SXSSFWorkbook();
                org.apache.poi.ss.usermodel.Sheet sheet = workbook.createSheet("核算结果");

                // 创建标题行
                org.apache.poi.ss.usermodel.Row headerRow = sheet.createRow(0);
                headerRow.createCell(0).setCellValue("人员姓名");
                headerRow.createCell(1).setCellValue("厂商名称");
                headerRow.createCell(2).setCellValue("核算月份");
                headerRow.createCell(3).setCellValue("标准工作时长");
                headerRow.createCell(4).setCellValue("实际打卡按9小时算");
                headerRow.createCell(5).setCellValue("人月工时");
                headerRow.createCell(6).setCellValue("结算金额");

                // 填充数据
                int rowNum = 1;
                BigDecimal totalAmount = BigDecimal.ZERO;
                for (SettlementResult result : resultList) {
                    org.apache.poi.ss.usermodel.Row row = sheet.createRow(rowNum++);
                    row.createCell(0).setCellValue(result.getStaffName());
                    row.createCell(1).setCellValue(result.getVendorName());
                    row.createCell(2).setCellValue(result.getMonth().toString());
                    row.createCell(3).setCellValue(result.getStandardHours().doubleValue());
                    row.createCell(4).setCellValue(result.getCalculatedHours().doubleValue());
                    row.createCell(5).setCellValue(result.getWorkDays().doubleValue());
                    row.createCell(6).setCellValue(result.getAmount().doubleValue());

                    // 累计总金额
                    totalAmount = totalAmount.add(result.getAmount());
                }

                // 添加合计行
                org.apache.poi.ss.usermodel.Row totalRow = sheet.createRow(rowNum);
                totalRow.createCell(0).setCellValue("合计");
                totalRow.createCell(6).setCellValue(totalAmount.doubleValue());

                // 自动调整列宽
                for (int i = 0; i < 7; i++) {
                    sheet.autoSizeColumn(i);
                }

                // 保存Excel文件
                FileOutputStream fileOut = new FileOutputStream(filePath);
                workbook.write(fileOut);
                fileOut.close();
                workbook.close();
                log.info("Excel文件导出成功：{}", filePath);
            } catch (Exception e) {
                log.error("导出Excel失败", e);
                throw new RuntimeException("导出Excel失败：" + e.getMessage(), e);
            }

            // 3. 发送邮件
            try {
                String subject = vendorName + " " + monthStr + " 月份核算结果";
                String content = "尊敬的客户：<br/><br/>附件为 " + monthStr + " 月份的核算结果，请查收。<br/><br/>此邮件为系统自动发送，请勿回复。";

                boolean sendResult = emailService.sendAttachmentMail(email, subject, content, filePath);
                if (sendResult) {
                    log.info("邮件发送成功，接收邮箱：{}", email);
                } else {
                    log.error("邮件发送失败，接收邮箱：{}", email);
                }
                return sendResult;
            } catch (Exception e) {
                log.error("发送邮件失败，接收邮箱：{}", email, e);
                throw new RuntimeException("发送邮件失败：" + e.getMessage(), e);
            }
        } catch (Exception e) {
            log.error("导出并发送核算结果失败，月份：{}，厂商ID：{}，接收邮箱：{}", month, vendorId, email, e);
            return false;
        }
    }
}
