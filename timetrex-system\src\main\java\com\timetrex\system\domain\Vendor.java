package com.timetrex.system.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.timetrex.common.annotation.Excel;
import com.timetrex.common.core.domain.BaseEntity;

/**
 * 厂商配置对象 vendor
 * 
 * <AUTHOR>
 * @date 2025-04-15
 */
public class Vendor extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 厂商名称 */
    @Excel(name = "厂商名称")
    private String name;

    /** 平均计薪工作日 */
    @Excel(name = "平均计薪工作日")
    private BigDecimal avgWorkDays;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setAvgWorkDays(BigDecimal avgWorkDays) 
    {
        this.avgWorkDays = avgWorkDays;
    }

    public BigDecimal getAvgWorkDays() 
    {
        return avgWorkDays;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("avgWorkDays", getAvgWorkDays())
            .toString();
    }
}
