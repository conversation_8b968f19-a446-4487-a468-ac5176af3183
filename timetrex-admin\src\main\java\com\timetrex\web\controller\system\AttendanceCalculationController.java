package com.timetrex.web.controller.system;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.timetrex.common.annotation.Log;
import com.timetrex.common.core.controller.BaseController;
import com.timetrex.common.core.domain.AjaxResult;
import com.timetrex.common.core.page.TableDataInfo;
import com.timetrex.common.enums.BusinessType;
import com.timetrex.common.utils.poi.ExcelUtil;
import com.timetrex.system.domain.SettlementResult;
import com.timetrex.system.domain.Vendor;
import com.timetrex.system.service.ISettlementResultService;
import com.timetrex.system.service.IVendorService;

/**
 * 考勤核算Controller
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Controller
@RequestMapping("/system/attendance/calculation")
public class AttendanceCalculationController extends BaseController
{
    private String prefix = "system/attendance/calculation";

    @Autowired
    private ISettlementResultService settlementResultService;

    @Autowired
    private IVendorService vendorService;

    @RequiresPermissions("system:attendance:calculation:view")
    @GetMapping()
    public String calculation(ModelMap mmap)
    {
        // 获取厂商列表
        List<Vendor> vendorList = vendorService.selectVendorList(new Vendor());
        mmap.put("vendorList", vendorList);
        return prefix + "/calculation";
    }

    /**
     * 查询核算结果列表
     */
    @RequiresPermissions("system:attendance:calculation:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SettlementResult settlementResult)
    {
        startPage();
        List<SettlementResult> list = settlementResultService.selectSettlementResultList(settlementResult);
        return getDataTable(list);
    }

    /**
     * 执行考勤核算
     */
    @RequiresPermissions("system:attendance:calculation:calculate")
    @Log(title = "考勤核算", businessType = BusinessType.OTHER)
    @PostMapping("/calculate")
    @ResponseBody
    public AjaxResult calculate(@RequestParam("month") String monthStr, @RequestParam("vendorIds") String vendorIdsStr)
    {
        try {
            // 解析月份
            LocalDate month = LocalDate.parse(monthStr + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // 解析厂商ID列表
            String[] vendorIdArray = vendorIdsStr.split(",");
            List<Long> vendorIds = new ArrayList<>();
            for (String idStr : vendorIdArray) {
                vendorIds.add(Long.parseLong(idStr));
            }

            if (vendorIds.isEmpty()) {
                return AjaxResult.error("请选择至少一个厂商");
            }

            // 执行核算（并行处理多个厂商）
            int totalCount = 0;

            // 如果只有一个厂商，直接处理
            if (vendorIds.size() == 1) {
                List<SettlementResult> resultList = settlementResultService.calculateSettlement(month, vendorIds.get(0));
                totalCount = resultList.size();
            } else {
                // 多个厂商并行处理
                final LocalDate finalMonth = month;
                List<List<SettlementResult>> allResults = vendorIds.parallelStream()
                    .map(vendorId -> {
                        try {
                            return settlementResultService.calculateSettlement(finalMonth, vendorId);
                        } catch (Exception e) {
                            logger.error("厂商ID: " + vendorId + " 核算失败", e);
                            return new ArrayList<SettlementResult>();
                        }
                    })
                    .collect(java.util.stream.Collectors.toList());

                // 计算总数
                for (List<SettlementResult> resultList : allResults) {
                    totalCount += resultList.size();
                }
            }

            return AjaxResult.success("核算成功，共生成 " + totalCount + " 条核算结果");
        } catch (Exception e) {
            logger.error("执行核算失败", e);
            return AjaxResult.error("核算失败：" + e.getMessage());
        }
    }

    /**
     * 导出核算结果
     */
    @RequiresPermissions("system:attendance:calculation:export")
    @Log(title = "导出核算结果", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SettlementResult settlementResult)
    {
        List<SettlementResult> list = settlementResultService.selectSettlementResultList(settlementResult);
        ExcelUtil<SettlementResult> util = new ExcelUtil<SettlementResult>(SettlementResult.class);
        return util.exportExcel(list, "核算结果");
    }

    /**
     * 导出并发送核算结果
     */
    @RequiresPermissions("system:attendance:calculation:send")
    @Log(title = "发送核算结果", businessType = BusinessType.EXPORT)
    @PostMapping("/sendResult")
    @ResponseBody
    public AjaxResult sendResult(@RequestParam("month") String monthStr,
                                @RequestParam("vendorId") Long vendorId,
                                @RequestParam("email") String email)
    {
        try {
            // 解析月份
            LocalDate month = LocalDate.parse(monthStr + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // 导出并发送
            boolean result = settlementResultService.exportAndSendSettlementResult(month, vendorId, email);

            if (result) {
                return AjaxResult.success("核算结果已成功发送至 " + email);
            } else {
                return AjaxResult.error("发送失败，请检查邮箱地址是否正确");
            }
        } catch (Exception e) {
            return AjaxResult.error("发送失败：" + e.getMessage());
        }
    }
}
