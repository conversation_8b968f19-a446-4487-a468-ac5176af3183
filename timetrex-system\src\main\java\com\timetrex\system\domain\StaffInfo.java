package com.timetrex.system.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.timetrex.common.annotation.Excel;
import com.timetrex.common.core.domain.BaseEntity;

/**
 * 人员信息对象 staff_info
 * 
 * <AUTHOR>
 * @date 2025-04-15
 */
public class StaffInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 工号 */
    @Excel(name = "工号")
    private String staffNo;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 所属厂商ID */
    @Excel(name = "所属厂商ID")
    private Long vendorId;

    /** 所属项目ID */
    @Excel(name = "所属项目ID")
    private Long projectId;

    /** 角色ID */
    @Excel(name = "角色ID")
    private Long roleId;

    /** 工作经验年限 */
    @Excel(name = "工作经验年限")
    private BigDecimal experienceYears;

    /** 单价(元/人月) */
    @Excel(name = "单价(元/人月)")
    private BigDecimal unitPrice;

    /** 人员级别 */
    @Excel(name = "人员级别")
    private Long staffLevel;

    /** 状态(1-在职 0-离职) */
    @Excel(name = "状态(1-在职 0-离职)")
    private Integer status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setStaffNo(String staffNo) 
    {
        this.staffNo = staffNo;
    }

    public String getStaffNo() 
    {
        return staffNo;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setVendorId(Long vendorId) 
    {
        this.vendorId = vendorId;
    }

    public Long getVendorId() 
    {
        return vendorId;
    }

    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }

    public void setRoleId(Long roleId) 
    {
        this.roleId = roleId;
    }

    public Long getRoleId() 
    {
        return roleId;
    }

    public void setExperienceYears(BigDecimal experienceYears)
    {
        this.experienceYears = experienceYears;
    }

    public BigDecimal getExperienceYears()
    {
        return experienceYears;
    }

    public void setUnitPrice(BigDecimal unitPrice) 
    {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getUnitPrice() 
    {
        return unitPrice;
    }

    public void setStaffLevel(Long staffLevel) 
    {
        this.staffLevel = staffLevel;
    }

    public Long getStaffLevel() 
    {
        return staffLevel;
    }

    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("staffNo", getStaffNo())
            .append("name", getName())
            .append("vendorId", getVendorId())
            .append("projectId", getProjectId())
            .append("roleId", getRoleId())
            .append("experienceYears", getExperienceYears())
            .append("unitPrice", getUnitPrice())
            .append("staffLevel", getStaffLevel())
            .append("status", getStatus())
            .toString();
    }
}
