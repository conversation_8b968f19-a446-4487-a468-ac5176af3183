package ${packageName}.controller;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.timetrex.common.annotation.Log;
import com.timetrex.common.enums.BusinessType;
import ${packageName}.domain.${ClassName};
import ${packageName}.service.I${ClassName}Service;
import com.timetrex.common.core.controller.BaseController;
import com.timetrex.common.core.domain.AjaxResult;
import com.timetrex.common.utils.poi.ExcelUtil;
#if($table.crud || $table.sub)
import com.timetrex.common.core.page.TableDataInfo;
#elseif($table.tree)
import com.timetrex.common.utils.StringUtils;
import com.timetrex.common.core.domain.Ztree;
#end

/**
 * ${functionName}Controller
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
@Controller
@RequestMapping("/${moduleName}/${businessName}")
public class ${ClassName}Controller extends BaseController
{
    private String prefix = "${moduleName}/${businessName}";

    @Autowired
    private I${ClassName}Service ${className}Service;

    @RequiresPermissions("${permissionPrefix}:view")
    @GetMapping()
    public String ${businessName}()
    {
        return prefix + "/${businessName}";
    }

#if($table.crud || $table.sub)
    /**
     * 查询${functionName}列表
     */
    @RequiresPermissions("${permissionPrefix}:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(${ClassName} ${className})
    {
        startPage();
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        return getDataTable(list);
    }
#elseif($table.tree)
    /**
     * 查询${functionName}树列表
     */
    @RequiresPermissions("${permissionPrefix}:list")
    @PostMapping("/list")
    @ResponseBody
    public List<${ClassName}> list(${ClassName} ${className})
    {
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        return list;
    }
#end

    /**
     * 导出${functionName}列表
     */
    @RequiresPermissions("${permissionPrefix}:export")
    @Log(title = "${functionName}", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(${ClassName} ${className})
    {
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        ExcelUtil<${ClassName}> util = new ExcelUtil<${ClassName}>(${ClassName}.class);
        return util.exportExcel(list, "${functionName}数据");
    }

#if($table.crud || $table.sub)
    /**
     * 新增${functionName}
     */
    @RequiresPermissions("${permissionPrefix}:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }
#elseif($table.tree)
    /**
     * 新增${functionName}
     */
    @GetMapping(value = { "/add/{${pkColumn.javaField}}", "/add/" })
    public String add(@PathVariable(value = "${pkColumn.javaField}", required = false) Long ${pkColumn.javaField}, ModelMap mmap)
    {
        if (StringUtils.isNotNull(${pkColumn.javaField}))
        {
            mmap.put("${className}", ${className}Service.select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField}));
        }
        return prefix + "/add";
    }
#end

    /**
     * 新增保存${functionName}
     */
    @RequiresPermissions("${permissionPrefix}:add")
    @Log(title = "${functionName}", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(${ClassName} ${className})
    {
        return toAjax(${className}Service.insert${ClassName}(${className}));
    }

    /**
     * 修改${functionName}
     */
    @RequiresPermissions("${permissionPrefix}:edit")
    @GetMapping("/edit/{${pkColumn.javaField}}")
    public String edit(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField}, ModelMap mmap)
    {
        ${ClassName} ${className} = ${className}Service.select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField});
        mmap.put("${className}", ${className});
        return prefix + "/edit";
    }

    /**
     * 修改保存${functionName}
     */
    @RequiresPermissions("${permissionPrefix}:edit")
    @Log(title = "${functionName}", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(${ClassName} ${className})
    {
        return toAjax(${className}Service.update${ClassName}(${className}));
    }

#if($table.crud || $table.sub)
    /**
     * 删除${functionName}
     */
    @RequiresPermissions("${permissionPrefix}:remove")
    @Log(title = "${functionName}", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(${className}Service.delete${ClassName}By${pkColumn.capJavaField}s(ids));
    }
#elseif($table.tree)
    /**
     * 删除
     */
    @RequiresPermissions("${permissionPrefix}:remove")
    @Log(title = "${functionName}", businessType = BusinessType.DELETE)
    @GetMapping("/remove/{${pkColumn.javaField}}")
    @ResponseBody
    public AjaxResult remove(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField})
    {
        return toAjax(${className}Service.delete${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField}));
    }
#end
#if($table.tree)

    /**
     * 选择${functionName}树
     */
#set($BusinessName=$businessName.substring(0,1).toUpperCase() + ${businessName.substring(1)})
    @GetMapping(value = { "/select${BusinessName}Tree/{${pkColumn.javaField}}", "/select${BusinessName}Tree/" })
    public String select${BusinessName}Tree(@PathVariable(value = "${pkColumn.javaField}", required = false) Long ${pkColumn.javaField}, ModelMap mmap)
    {
        if (StringUtils.isNotNull(${pkColumn.javaField}))
        {
            mmap.put("${className}", ${className}Service.select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField}));
        }
        return prefix + "/tree";
    }

    /**
     * 加载${functionName}树列表
     */
    @GetMapping("/treeData")
    @ResponseBody
    public List<Ztree> treeData()
    {
        List<Ztree> ztrees = ${className}Service.select${ClassName}Tree();
        return ztrees;
    }
#end
}
