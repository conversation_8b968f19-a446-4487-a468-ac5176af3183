# TimeTrex Technical Context

## Core Technologies
- Java 1.8
- Spring Boot 2.5.15
- Spring Framework 5.3.39
- Apache Shiro 1.13.0 (Authentication/Authorization)
- Thymeleaf with Shiro extensions 2.1.0
- Alibaba Druid 1.2.23 (Database connection pool)

## Build System
- Maven (pom.xml)
- Java 8 compatibility
- UTF-8 encoding
- Aliyun Maven repository

## Key Dependencies
### Security
- Shiro Core
- Shiro Spring integration
- Shiro EhCache integration

### Web & API
- Tomcat 9.0.102 (embedded)
- Swagger 3.0.0 (API documentation)
- Thymeleaf templates

### Data & Utilities
- PageHelper 1.4.7 (Pagination)
- FastJSON 1.2.83 (JSON processing)
- POI 4.1.2 (Excel handling)
- Commons IO 2.16.1

### System Monitoring
- OSHI 6.6.5 (System information)
- UserAgentUtils 1.21 (Client detection)

### Modules
- timetrex-admin (Web interface)
- timetrex-framework (Core infrastructure)
- timetrex-system (System services)
- timetrex-quartz (Scheduled jobs)
- timetrex-common (Shared utilities)
- timetrex-generator (Code generation)

## Development Setup
1. Maven-based project structure
2. Module dependencies managed via pom.xml
3. Standard Maven build lifecycle
4. UTF-8 encoding enforced
5. Tomcat embedded server for development
