-- 添加季度归总报表菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('季度归总报表', (SELECT menu_id FROM sys_menu WHERE menu_name = '考勤管理' AND parent_id = 0), 4, '/system/quarterly/report', '', 'C', '0', 'system:quarterly:report:view', 'fa fa-bar-chart', 'admin', SYSDATE(), 'admin', SYSDATE(), '季度归总报表菜单');

-- 添加查询按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('季度归总报表查询', (SELECT menu_id FROM sys_menu WHERE menu_name = '季度归总报表' AND parent_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '考勤管理' AND parent_id = 0)), 1, '#', '', 'F', '0', 'system:quarterly:report:list', '#', 'admin', SYSDATE(), 'admin', SYSDATE(), '');

-- 添加导出按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, url, target, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('季度归总报表导出', (SELECT menu_id FROM sys_menu WHERE menu_name = '季度归总报表' AND parent_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '考勤管理' AND parent_id = 0)), 2, '#', '', 'F', '0', 'system:quarterly:report:export', '#', 'admin', SYSDATE(), 'admin', SYSDATE(), '');
