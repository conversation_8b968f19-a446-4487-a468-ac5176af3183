package com.timetrex.system.service;

import java.util.List;
import java.util.Map;
import com.timetrex.system.domain.StaffWorkloadRecord;

/**
 * 人员工作量登记Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface IStaffWorkloadRecordService 
{
    /**
     * 查询人员工作量登记
     * 
     * @param id 人员工作量登记主键
     * @return 人员工作量登记
     */
    public StaffWorkloadRecord selectStaffWorkloadRecordById(Long id);

    /**
     * 查询人员工作量登记列表
     * 
     * @param staffWorkloadRecord 人员工作量登记
     * @return 人员工作量登记集合
     */
    public List<StaffWorkloadRecord> selectStaffWorkloadRecordList(StaffWorkloadRecord staffWorkloadRecord);

    /**
     * 新增人员工作量登记
     * 
     * @param staffWorkloadRecord 人员工作量登记
     * @return 结果
     */
    public int insertStaffWorkloadRecord(StaffWorkloadRecord staffWorkloadRecord);

    /**
     * 修改人员工作量登记
     * 
     * @param staffWorkloadRecord 人员工作量登记
     * @return 结果
     */
    public int updateStaffWorkloadRecord(StaffWorkloadRecord staffWorkloadRecord);

    /**
     * 批量删除人员工作量登记
     * 
     * @param ids 需要删除的人员工作量登记主键集合
     * @return 结果
     */
    public int deleteStaffWorkloadRecordByIds(Long[] ids);

    /**
     * 删除人员工作量登记信息
     * 
     * @param id 人员工作量登记主键
     * @return 结果
     */
    public int deleteStaffWorkloadRecordById(Long id);

    /**
     * 提交工作量登记
     * 
     * @param ids 主键集合
     * @return 结果
     */
    public int submitWorkloadRecords(Long[] ids);

    /**
     * 计算工作量结算金额
     * 
     * @param vendorId 厂商ID
     * @param recordYear 年份
     * @param recordMonth 月份
     * @param calculateUser 计算人员
     * @return 计算结果
     */
    public Map<String, Object> calculateSettlementAmount(Long vendorId, Integer recordYear, Integer recordMonth, String calculateUser);

    /**
     * 审批工作量登记
     * 
     * @param ids 主键集合
     * @param approveUser 审批人员
     * @return 结果
     */
    public int approveWorkloadRecords(Long[] ids, String approveUser);

    /**
     * 根据厂商ID和年月查询工作量登记
     * 
     * @param vendorId 厂商ID
     * @param recordYear 年份
     * @param recordMonth 月份
     * @return 工作量登记列表
     */
    public List<StaffWorkloadRecord> selectByVendorAndMonth(Long vendorId, Integer recordYear, Integer recordMonth);

    /**
     * 检查是否可以删除工作量登记
     * 
     * @param id 主键
     * @return 是否可以删除
     */
    public boolean canDelete(Long id);

    /**
     * 检查是否可以修改工作量登记
     * 
     * @param id 主键
     * @return 是否可以修改
     */
    public boolean canEdit(Long id);

    /**
     * 获取工作量统计数据
     * 
     * @param vendorId 厂商ID
     * @param recordYear 年份
     * @param recordMonth 月份
     * @return 统计数据
     */
    public Map<String, Object> getWorkloadStatistics(Long vendorId, Integer recordYear, Integer recordMonth);
}
