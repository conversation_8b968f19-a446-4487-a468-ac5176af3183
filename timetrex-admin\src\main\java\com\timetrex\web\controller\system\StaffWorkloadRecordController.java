package com.timetrex.web.controller.system;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import com.timetrex.common.annotation.Log;
import com.timetrex.common.enums.BusinessType;
import com.timetrex.system.domain.StaffWorkloadRecord;
import com.timetrex.system.domain.Vendor;
import com.timetrex.system.service.IStaffWorkloadRecordService;
import com.timetrex.system.service.IVendorService;
import com.timetrex.common.core.controller.BaseController;
import com.timetrex.common.core.domain.AjaxResult;
import com.timetrex.common.utils.poi.ExcelUtil;
import com.timetrex.common.core.page.TableDataInfo;

/**
 * 人员工作量登记Controller
 * 
 * <AUTHOR>
 * @date 2024-12-20
 */
@Controller
@RequestMapping("/system/workload/record")
public class StaffWorkloadRecordController extends BaseController
{
    private String prefix = "system/workload/record";

    @Autowired
    private IStaffWorkloadRecordService staffWorkloadRecordService;
    
    @Autowired
    private IVendorService vendorService;

    @RequiresPermissions("system:workload:record:view")
    @GetMapping()
    public String record(ModelMap mmap)
    {
        // 获取厂商列表
        List<Vendor> vendorList = vendorService.selectVendorList(new Vendor());
        mmap.put("vendorList", vendorList);
        
        // 获取当前年份
        int currentYear = java.time.LocalDate.now().getYear();
        mmap.put("currentYear", currentYear);
        
        return prefix + "/record";
    }

    /**
     * 查询人员工作量登记列表
     */
    @RequiresPermissions("system:workload:record:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(StaffWorkloadRecord staffWorkloadRecord)
    {
        startPage();
        List<StaffWorkloadRecord> list = staffWorkloadRecordService.selectStaffWorkloadRecordList(staffWorkloadRecord);
        return getDataTable(list);
    }

    /**
     * 导出人员工作量登记列表
     */
    @RequiresPermissions("system:workload:record:export")
    @Log(title = "人员工作量登记", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(StaffWorkloadRecord staffWorkloadRecord)
    {
        List<StaffWorkloadRecord> list = staffWorkloadRecordService.selectStaffWorkloadRecordList(staffWorkloadRecord);
        ExcelUtil<StaffWorkloadRecord> util = new ExcelUtil<StaffWorkloadRecord>(StaffWorkloadRecord.class);
        return util.exportExcel(list, "人员工作量登记数据");
    }

    /**
     * 新增人员工作量登记
     */
    @GetMapping("/add")
    public String add(ModelMap mmap)
    {
        // 获取厂商列表
        List<Vendor> vendorList = vendorService.selectVendorList(new Vendor());
        mmap.put("vendorList", vendorList);
        
        return prefix + "/add";
    }

    /**
     * 新增保存人员工作量登记
     */
    @RequiresPermissions("system:workload:record:add")
    @Log(title = "人员工作量登记", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(StaffWorkloadRecord staffWorkloadRecord)
    {
        staffWorkloadRecord.setCreateBy(getUsername());
        return toAjax(staffWorkloadRecordService.insertStaffWorkloadRecord(staffWorkloadRecord));
    }

    /**
     * 修改人员工作量登记
     */
    @RequiresPermissions("system:workload:record:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        StaffWorkloadRecord staffWorkloadRecord = staffWorkloadRecordService.selectStaffWorkloadRecordById(id);
        mmap.put("staffWorkloadRecord", staffWorkloadRecord);
        
        // 获取厂商列表
        List<Vendor> vendorList = vendorService.selectVendorList(new Vendor());
        mmap.put("vendorList", vendorList);
        
        // 检查是否可以编辑
        boolean canEdit = staffWorkloadRecordService.canEdit(id);
        mmap.put("canEdit", canEdit);
        
        return prefix + "/edit";
    }

    /**
     * 修改保存人员工作量登记
     */
    @RequiresPermissions("system:workload:record:edit")
    @Log(title = "人员工作量登记", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(StaffWorkloadRecord staffWorkloadRecord)
    {
        // 检查是否可以编辑
        if (!staffWorkloadRecordService.canEdit(staffWorkloadRecord.getId())) {
            return AjaxResult.error("当前状态不允许修改");
        }
        
        staffWorkloadRecord.setUpdateBy(getUsername());
        return toAjax(staffWorkloadRecordService.updateStaffWorkloadRecord(staffWorkloadRecord));
    }

    /**
     * 删除人员工作量登记
     */
    @RequiresPermissions("system:workload:record:remove")
    @Log(title = "人员工作量登记", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        try {
            return toAjax(staffWorkloadRecordService.deleteStaffWorkloadRecordByIds(getLongArrFromStr(ids)));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 提交工作量登记
     */
    @RequiresPermissions("system:workload:record:edit")
    @Log(title = "提交工作量登记", businessType = BusinessType.UPDATE)
    @PostMapping("/submit")
    @ResponseBody
    public AjaxResult submit(String ids)
    {
        try {
            return toAjax(staffWorkloadRecordService.submitWorkloadRecords(getLongArrFromStr(ids)));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 计算结算金额
     */
    @RequiresPermissions("system:workload:record:calculate")
    @Log(title = "计算结算金额", businessType = BusinessType.UPDATE)
    @PostMapping("/calculate")
    @ResponseBody
    public AjaxResult calculate(@RequestParam Long vendorId, 
                               @RequestParam Integer recordYear, 
                               @RequestParam Integer recordMonth)
    {
        try {
            Map<String, Object> result = staffWorkloadRecordService.calculateSettlementAmount(
                vendorId, recordYear, recordMonth, getUsername());
            return AjaxResult.success("计算完成", result);
        } catch (Exception e) {
            return AjaxResult.error("计算失败：" + e.getMessage());
        }
    }

    /**
     * 审批工作量登记
     */
    @RequiresPermissions("system:workload:record:approve")
    @Log(title = "审批工作量登记", businessType = BusinessType.UPDATE)
    @PostMapping("/approve")
    @ResponseBody
    public AjaxResult approve(String ids)
    {
        try {
            return toAjax(staffWorkloadRecordService.approveWorkloadRecords(getLongArrFromStr(ids), getUsername()));
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取工作量统计
     */
    @RequiresPermissions("system:workload:record:query")
    @PostMapping("/statistics")
    @ResponseBody
    public AjaxResult statistics(@RequestParam Long vendorId, 
                                @RequestParam Integer recordYear, 
                                @RequestParam Integer recordMonth)
    {
        try {
            Map<String, Object> result = staffWorkloadRecordService.getWorkloadStatistics(vendorId, recordYear, recordMonth);
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("获取统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 校验员工工号和月份的唯一性
     */
    @PostMapping("/checkUnique")
    @ResponseBody
    public AjaxResult checkUnique(StaffWorkloadRecord staffWorkloadRecord)
    {
        // 这里可以添加唯一性校验逻辑
        return AjaxResult.success();
    }
}
