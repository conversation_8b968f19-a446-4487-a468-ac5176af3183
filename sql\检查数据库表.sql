-- 检查数据库表是否存在
-- 1. 检查staff_workload_record表是否存在
SELECT TABLE_NAME, TABLE_COMMENT 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'staff_workload_record';

-- 2. 检查表结构
DESCRIBE staff_workload_record;

-- 3. 检查staff_info表是否存在vendor_id和unit_price字段
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'staff_info' 
AND COLUMN_NAME IN ('vendor_id', 'unit_price');

-- 4. 检查vendor表是否存在avg_work_days字段
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'vendor' 
AND COLUMN_NAME = 'avg_work_days';

-- 5. 检查是否有测试数据
SELECT COUNT(*) as staff_count FROM staff_info;
SELECT COUNT(*) as vendor_count FROM vendor;

-- 6. 查看staff_info表的示例数据
SELECT id, staff_no, name, vendor_id, unit_price, status 
FROM staff_info 
LIMIT 5;

-- 7. 查看vendor表的示例数据
SELECT id, name, avg_work_days 
FROM vendor 
LIMIT 5;
