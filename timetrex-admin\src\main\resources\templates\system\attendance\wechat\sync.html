<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('企业微信打卡数据同步')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="sync-form">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>年月：</label>
                                <input type="text" class="form-control" id="yearMonth" name="yearMonth" placeholder="请选择年月" readonly>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="sync()"><i class="fa fa-sync"></i> 同步</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            
            <div class="col-sm-12 select-table table-striped">
                <div class="btn-group-sm" id="toolbar" role="group">
                    <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:attendance:wechat:add">
                        <i class="fa fa-plus"></i> 新增
                    </a>
                </div>
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "system/attendance/wechat";
        
        $(function() {
            // 初始化年月选择器
            laydate.render({
                elem: '#yearMonth',
                type: 'month',
                format: 'yyyy-MM',
                value: new Date()
            });
            
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "同步记录",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'yearMonth',
                    title: '年月'
                },
                {
                    field: 'syncTime',
                    title: '同步时间'
                },
                {
                    field: 'summaryCount',
                    title: '概况数据条数'
                },
                {
                    field: 'detailCount',
                    title: '详情数据条数'
                },
                {
                    field: 'status',
                    title: '状态',
                    formatter: function(value, row, index) {
                        if (value == 0) {
                            return '<span class="badge badge-success">成功</span>';
                        } else {
                            return '<span class="badge badge-danger">失败</span>';
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
        
        // 同步企业微信打卡数据
        function sync() {
            var yearMonth = $("#yearMonth").val();
            if (!yearMonth) {
                $.modal.alertWarning("请选择要同步的年月");
                return;
            }
            
            $.modal.confirm("确定要同步 " + yearMonth + " 的企业微信打卡数据吗？", function() {
                $.modal.loading("正在同步数据，请稍候...");
                $.ajax({
                    type: "POST",
                    url: prefix + "/sync",
                    data: {
                        "yearMonth": yearMonth
                    },
                    success: function(result) {
                        $.modal.closeLoading();
                        if (result.code == web_status.SUCCESS) {
                            $.modal.alertSuccess(result.msg);
                            $.table.refresh();
                        } else {
                            $.modal.alertError(result.msg);
                        }
                    },
                    error: function() {
                        $.modal.closeLoading();
                        $.modal.alertError("同步失败，请稍后重试");
                    }
                });
            });
        }
    </script>
</body>
</html>