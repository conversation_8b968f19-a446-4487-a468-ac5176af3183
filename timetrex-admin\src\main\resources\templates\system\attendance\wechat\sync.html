<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('企业微信打卡数据同步')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="sync-form">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>年月：</label>
                                <input type="text" class="form-control" id="yearMonth" name="yearMonth" placeholder="请选择年月" readonly>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="sync()"><i class="fa fa-sync"></i> 同步</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>同步说明</h5>
                    </div>
                    <div class="ibox-content">
                        <div class="alert alert-info">
                            <h4>企业微信打卡数据同步说明：</h4>
                            <ul>
                                <li>1. 选择要同步的年月，系统将获取该月份的企业微信打卡数据</li>
                                <li>2. 同步的数据包括：打卡日报统计数据和详细打卡记录</li>
                                <li>3. 同步后的数据将与Excel导出功能共用同一套数据表</li>
                                <li>4. 企业微信返回的时间戳将自动转换为北京时间</li>
                                <li>5. 请确保已正确配置企业微信的corpid和secret</li>
                            </ul>
                        </div>
                        <div id="sync-result" style="display: none;">
                            <div class="alert alert-success">
                                <h4>同步结果：</h4>
                                <p id="sync-message"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "system/attendance/wechat";

        $(function() {
            // 使用layui的laydate模块
            layui.use('laydate', function(){
                var laydate = layui.laydate;

                // 初始化年月选择器
                laydate.render({
                    elem: '#yearMonth',
                    type: 'month',
                    format: 'yyyy-MM',
                    value: new Date(),
                    max: 0, // 不能选择未来的月份
                    theme: 'molv'
                });
            });
        });

        // 同步企业微信打卡数据
        function sync() {
            var yearMonth = $("#yearMonth").val();
            if (!yearMonth) {
                $.modal.alertWarning("请选择要同步的年月");
                return;
            }

            $.modal.confirm("确定要同步 " + yearMonth + " 的企业微信打卡数据吗？", function() {
                $.modal.loading("正在同步数据，请稍候...");
                $.ajax({
                    type: "POST",
                    url: prefix + "/sync",
                    data: {
                        "yearMonth": yearMonth
                    },
                    success: function(result) {
                        $.modal.closeLoading();
                        if (result.code == web_status.SUCCESS) {
                            $("#sync-message").text(result.msg);
                            $("#sync-result").show();
                            $.modal.alertSuccess(result.msg);
                        } else {
                            $.modal.alertError(result.msg);
                        }
                    },
                    error: function() {
                        $.modal.closeLoading();
                        $.modal.alertError("同步失败，请稍后重试");
                    }
                });
            });
        }
    </script>
</body>
</html>