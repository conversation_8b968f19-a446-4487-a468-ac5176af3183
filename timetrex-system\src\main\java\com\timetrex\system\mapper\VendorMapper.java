package com.timetrex.system.mapper;

import java.util.List;
import com.timetrex.system.domain.Vendor;

/**
 * 厂商配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface VendorMapper 
{
    /**
     * 查询厂商配置
     * 
     * @param id 厂商配置主键
     * @return 厂商配置
     */
    public Vendor selectVendorById(Long id);

    /**
     * 查询厂商配置列表
     * 
     * @param vendor 厂商配置
     * @return 厂商配置集合
     */
    public List<Vendor> selectVendorList(Vendor vendor);

    /**
     * 新增厂商配置
     * 
     * @param vendor 厂商配置
     * @return 结果
     */
    public int insertVendor(Vendor vendor);

    /**
     * 修改厂商配置
     * 
     * @param vendor 厂商配置
     * @return 结果
     */
    public int updateVendor(Vendor vendor);

    /**
     * 删除厂商配置
     * 
     * @param id 厂商配置主键
     * @return 结果
     */
    public int deleteVendorById(Long id);

    /**
     * 批量删除厂商配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVendorByIds(String[] ids);
}
