package com.timetrex.system.utils;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.Locale;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.timetrex.system.domain.AttendanceDetail;
import com.timetrex.system.domain.AttendanceSummary;

/**
 * 考勤数据导入工具类
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
public class AttendanceImportUtil {

    private static final Logger log = LoggerFactory.getLogger(AttendanceImportUtil.class);

    /**
     * 从Excel导入考勤数据
     *
     * @param inputStream Excel文件输入流
     * @return 导入结果，包含考勤概况和打卡详情
     * @throws IOException
     */
    public static Map<String, Object> importAttendanceData(InputStream inputStream) throws IOException {
        Map<String, Object> result = new HashMap<>();
        List<AttendanceSummary> summaryList = new ArrayList<>();
        List<AttendanceDetail> detailList = new ArrayList<>();
        List<String> errorMessages = new ArrayList<>();
        List<String> warningMessages = new ArrayList<>(); // 新增警告消息列表
        // 使用Map<设备ID, Set<员工编号>>来记录每个设备ID关联的不同员工
        Map<String, Set<String>> deviceUserMap = new HashMap<>();

        try (Workbook workbook = WorkbookFactory.create(inputStream)) {
            // 检查工作簿是否包含两个工作表
            if (workbook.getNumberOfSheets() < 2) {
                errorMessages.add("Excel文件必须包含两个工作表：概况统计与打卡明细、打卡详情");
                result.put("success", false);
                result.put("errorMessages", errorMessages);
                return result;
            }

            // 导入概况统计与打卡明细
            Sheet summarySheet = workbook.getSheetAt(0);
            summaryList = importSummarySheet(summarySheet, errorMessages);

            // 导入打卡详情
            Sheet detailSheet = workbook.getSheetAt(1);
            detailList = importDetailSheet(detailSheet, errorMessages, deviceUserMap);

            // 检查设备ID是否被多人使用
            List<String> multiUserDevices = new ArrayList<>();
            for (Map.Entry<String, Set<String>> entry : deviceUserMap.entrySet()) {
                // 如果一个设备ID关联了多个不同的员工编号，则认为是被多人使用
                if (entry.getValue().size() > 1) {
                    multiUserDevices.add(entry.getKey());
                }
            }

            if (!multiUserDevices.isEmpty()) {
                StringBuilder warningMsg = new StringBuilder("【警告】以下设备ID被多人使用（这是一个软提示，不影响导入）：");
                for (String deviceId : multiUserDevices) {
                    Set<String> users = deviceUserMap.get(deviceId);
                    warningMsg.append("\n设备ID: ").append(deviceId).append(", 使用人员: ").append(String.join(", ", users));
                }
                warningMessages.add(warningMsg.toString());
                log.warn(warningMsg.toString());
            }

            result.put("success", true);
            result.put("summaryList", summaryList);
            result.put("detailList", detailList);
            result.put("errorMessages", errorMessages);
            result.put("warningMessages", warningMessages); // 添加警告消息
            result.put("multiUserDevices", multiUserDevices);

            return result;
        }
    }

    // 概况统计与打卡明细工作表的必需列
    private static final String[] SUMMARY_REQUIRED_COLUMNS = {
        "时间", "姓名", "账号", "部门", "职务", "所属规则", "班次",
        "最早", "最晚", "打卡次数(次)", "标准工作时长(小时)", "实际工作时长(小时)"
    };

    /**
     * 导入概况统计与打卡明细
     *
     * @param sheet 工作表
     * @param errorMessages 错误消息列表
     * @return 考勤概况列表
     */
    private static List<AttendanceSummary> importSummarySheet(Sheet sheet, List<String> errorMessages) {
        List<AttendanceSummary> summaryList = new ArrayList<>();

        // 获取标题行 - 直接使用第4行作为标题行
        Row headerRow = sheet.getRow(3);
        if (headerRow == null) {
            errorMessages.add("概况统计与打卡明细工作表缺少标题行");
            return summaryList;
        }

        // 输出标题行内容，帮助调试
        StringBuilder headerContent = new StringBuilder("概况统计与打卡明细工作表标题行内容：");
        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            Cell cell = headerRow.getCell(i);
            if (cell != null) {
                String cellValue = "";
                if (cell.getCellType() == CellType.STRING) {
                    cellValue = cell.getStringCellValue();
                } else if (cell.getCellType() == CellType.NUMERIC) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                } else if (cell.getCellType() == CellType.BOOLEAN) {
                    cellValue = String.valueOf(cell.getBooleanCellValue());
                }
                headerContent.append(" [").append(i).append(":").append(cellValue).append("]");
            }
        }
        log.info(headerContent.toString());

        // 创建列映射器
        ExcelColumnMapper columnMapper = new ExcelColumnMapper(SUMMARY_REQUIRED_COLUMNS);
        if (!columnMapper.initialize(headerRow)) {
            errorMessages.add("概况统计与打卡明细工作表缺少必需的列，请检查模板格式");
            log.error("概况统计与打卡明细工作表列映射初始化失败，缺少必需的列");
            return summaryList;
        }

        // 记录列映射情况
        log.info("概况统计与打卡明细工作表列映射: {}", columnMapper.getColumnMap());

        // 解析数据行 - 从第5行开始
        for (int i = 5; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }

            try {
                AttendanceSummary summary = new AttendanceSummary();

                // 解析各字段
                Cell dateCell = columnMapper.getCell(row, "时间");
                summary.setRecordDate(getLocalDateValue(dateCell));

                // 如果单元格包含星期几信息，直接提取
                if (dateCell != null && dateCell.getCellType() == CellType.STRING) {
                    String cellValue = dateCell.getStringCellValue();
                    if (cellValue.contains("星期")) {
                        String[] parts = cellValue.split(" ");
                        if (parts.length > 1) {
                            summary.setDayOfWeek(parts[1]);
                        }
                    }
                }
                summary.setName(getStringValue(columnMapper.getCell(row, "姓名")));
                summary.setStaffNo(getStringValue(columnMapper.getCell(row, "账号")));
                summary.setDepartment(getStringValue(columnMapper.getCell(row, "部门")));
                summary.setPosition(getStringValue(columnMapper.getCell(row, "职务")));
                summary.setWorkRule(getStringValue(columnMapper.getCell(row, "所属规则")));
                summary.setShift(getStringValue(columnMapper.getCell(row, "班次")));
                summary.setEarliestCheck(getLocalTimeValue(columnMapper.getCell(row, "最早")));
                summary.setLatestCheck(getLocalTimeValue(columnMapper.getCell(row, "最晚")));
                summary.setCheckCount(getIntegerValue(columnMapper.getCell(row, "打卡次数")));
                summary.setStandardHours(getBigDecimalValue(columnMapper.getCell(row, "标准工作时长(小时)")));
                summary.setActualHours(getBigDecimalValue(columnMapper.getCell(row, "实际工作时长(小时)")));

                // 计算实际打卡按9小时算
                if (summary.getActualHours() != null) {
                    if (summary.getActualHours().compareTo(new BigDecimal("9")) >= 0) {
                        summary.setCalculatedHours(new BigDecimal("9"));
                    } else {
                        summary.setCalculatedHours(new BigDecimal("4"));
                    }
                }

                // 设置星期几（如果之前没有从单元格直接提取到）
                if (summary.getRecordDate() != null && summary.getDayOfWeek() == null) {
                    DayOfWeek dayOfWeek = summary.getRecordDate().getDayOfWeek();
                    String dayOfWeekStr = dayOfWeek.getDisplayName(TextStyle.FULL, Locale.CHINA);
                    summary.setDayOfWeek(dayOfWeekStr);
                }

                summaryList.add(summary);
            } catch (Exception e) {
                errorMessages.add("导入概况统计与打卡明细第" + (i + 1) + "行时出错：" + e.getMessage());
                log.error("导入概况统计与打卡明细第" + (i + 1) + "行时出错", e);
            }
        }

        return summaryList;
    }

    // 打卡详情工作表的必需列
    private static final String[] DETAIL_REQUIRED_COLUMNS = {
        "日期", "姓名", "账号", "部门", "职务", "所属规则", "打卡类型",
        "应打卡时间", "实际打卡时间", "打卡状态", "打卡地点", "打卡设备"
    };

    /**
     * 导入打卡详情
     *
     * @param sheet 工作表
     * @param errorMessages 错误消息列表
     * @param deviceUserMap 设备ID与使用该设备的员工编号映射
     * @return 打卡详情列表
     */
    private static List<AttendanceDetail> importDetailSheet(Sheet sheet, List<String> errorMessages, Map<String, Set<String>> deviceUserMap) {
        List<AttendanceDetail> detailList = new ArrayList<>();

        // 获取标题行 - 直接使用第4行作为标题行
        Row headerRow = sheet.getRow(3);
        if (headerRow == null) {
            errorMessages.add("打卡详情工作表缺少标题行");
            return detailList;
        }

        // 输出标题行内容，帮助调试
        StringBuilder headerContent = new StringBuilder("打卡详情工作表标题行内容：");
        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            Cell cell = headerRow.getCell(i);
            if (cell != null) {
                String cellValue = "";
                if (cell.getCellType() == CellType.STRING) {
                    cellValue = cell.getStringCellValue();
                } else if (cell.getCellType() == CellType.NUMERIC) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                } else if (cell.getCellType() == CellType.BOOLEAN) {
                    cellValue = String.valueOf(cell.getBooleanCellValue());
                }
                headerContent.append(" [").append(i).append(":").append(cellValue).append("]");
            }
        }
        log.info(headerContent.toString());

        // 创建列映射器
        ExcelColumnMapper columnMapper = new ExcelColumnMapper(DETAIL_REQUIRED_COLUMNS);
        if (!columnMapper.initialize(headerRow)) {
            errorMessages.add("打卡详情工作表缺少必需的列，请检查模板格式");
            log.error("打卡详情工作表列映射初始化失败，缺少必需的列");
            return detailList;
        }

        // 记录列映射情况
        log.info("打卡详情工作表列映射: {}", columnMapper.getColumnMap());

        // 解析数据行 - 从第5行开始
        for (int i = 5; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }

            try {
                AttendanceDetail detail = new AttendanceDetail();

                // 解析各字段
                Cell dateCell = columnMapper.getCell(row, "日期");
                detail.setCheckDate(getLocalDateValue(dateCell));

                // 如果单元格包含星期几信息，直接提取
                if (dateCell != null && dateCell.getCellType() == CellType.STRING) {
                    String cellValue = dateCell.getStringCellValue();
                    if (cellValue.contains("星期")) {
                        String[] parts = cellValue.split(" ");
                        if (parts.length > 1) {
                            detail.setDayOfWeek(parts[1]);
                        }
                    }
                }
                detail.setName(getStringValue(columnMapper.getCell(row, "姓名")));
                detail.setStaffNo(getStringValue(columnMapper.getCell(row, "账号")));
                detail.setDepartment(getStringValue(columnMapper.getCell(row, "部门")));
                detail.setPosition(getStringValue(columnMapper.getCell(row, "职务")));
                detail.setWorkRule(getStringValue(columnMapper.getCell(row, "所属规则")));
                detail.setCheckType(getStringValue(columnMapper.getCell(row, "打卡类型")));
                detail.setScheduledTime(getLocalTimeValue(columnMapper.getCell(row, "应打卡时间")));
                detail.setActualTime(getLocalTimeValue(columnMapper.getCell(row, "实际打卡时间")));
                detail.setCheckStatus(getStringValue(columnMapper.getCell(row, "打卡状态")));
                detail.setLocation(getStringValue(columnMapper.getCell(row, "打卡地点")));
                detail.setDeviceId(getStringValue(columnMapper.getCell(row, "打卡设备")));

                // 备注和请假申请是可选字段
                Cell remarkCell = columnMapper.getCell(row, "备注");
                if (remarkCell != null) {
                    detail.setRemark(getStringValue(remarkCell));
                }

                Cell leaveCell = columnMapper.getCell(row, "请假申请");
                if (leaveCell != null) {
                    detail.setLeaveApplication(getStringValue(leaveCell));
                }

                // 设置星期几（如果之前没有从单元格直接提取到）
                if (detail.getCheckDate() != null && detail.getDayOfWeek() == null) {
                    DayOfWeek dayOfWeek = detail.getCheckDate().getDayOfWeek();
                    String dayOfWeekStr = dayOfWeek.getDisplayName(TextStyle.FULL, Locale.CHINA);
                    detail.setDayOfWeek(dayOfWeekStr);
                }

                // 记录设备ID与员工编号的关联关系
                if (detail.getDeviceId() != null && !detail.getDeviceId().isEmpty() && detail.getStaffNo() != null && !detail.getStaffNo().isEmpty()) {
                    String deviceId = detail.getDeviceId();
                    String staffNo = detail.getStaffNo();

                    // 如果设备ID不存在于Map中，则创建一个新的Set
                    if (!deviceUserMap.containsKey(deviceId)) {
                        deviceUserMap.put(deviceId, new HashSet<>());
                    }

                    // 将员工编号添加到对应设备ID的Set中
                    deviceUserMap.get(deviceId).add(staffNo);

                    // 记录设备ID与员工编号的关联关系
                    log.debug("记录设备ID与员工编号的关联关系: 设备ID={}, 员工编号={}", deviceId, staffNo);
                }

                detailList.add(detail);
            } catch (Exception e) {
                errorMessages.add("导入打卡详情第" + (i + 1) + "行时出错：" + e.getMessage());
                log.error("导入打卡详情第" + (i + 1) + "行时出错", e);
            }
        }

        return detailList;
    }

    /**
     * 获取单元格字符串值
     */
    private static String getStringValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        if (cell.getCellType() == CellType.STRING) {
            return cell.getStringCellValue();
        } else if (cell.getCellType() == CellType.NUMERIC) {
            return String.valueOf((int) cell.getNumericCellValue());
        } else if (cell.getCellType() == CellType.BOOLEAN) {
            return String.valueOf(cell.getBooleanCellValue());
        } else if (cell.getCellType() == CellType.FORMULA) {
            return cell.getCellFormula();
        } else {
            return null;
        }
    }

    /**
     * 获取单元格整数值
     * 如果单元格包含汉字或其他非数字字符，只提取数字部分
     */
    private static Integer getIntegerValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        if (cell.getCellType() == CellType.NUMERIC) {
            return (int) cell.getNumericCellValue();
        } else if (cell.getCellType() == CellType.STRING) {
            String cellValue = cell.getStringCellValue().trim();
            if (cellValue.isEmpty()) {
                return null;
            }

            try {
                // 尝试直接转换为Integer
                return Integer.parseInt(cellValue);
            } catch (NumberFormatException e) {
                // 如果直接转换失败，尝试提取数字部分
                StringBuilder numericPart = new StringBuilder();

                for (int i = 0; i < cellValue.length(); i++) {
                    char c = cellValue.charAt(i);
                    if (Character.isDigit(c)) {
                        numericPart.append(c);
                    }
                }

                if (numericPart.length() > 0) {
                    try {
                        int result = Integer.parseInt(numericPart.toString());
                        log.info("从包含非数字字符的单元格值 [{}] 中提取数字部分 [{}]", cellValue, result);
                        return result;
                    } catch (NumberFormatException ex) {
                        log.warn("无法从单元格值 [{}] 中提取有效的整数", cellValue);
                        return null;
                    }
                } else {
                    log.warn("单元格值 [{}] 不包含任何数字", cellValue);
                    return null;
                }
            }
        } else {
            return null;
        }
    }

    /**
     * 获取单元格BigDecimal值
     * 如果单元格包含汉字或其他非数字字符，只提取数字部分
     */
    private static BigDecimal getBigDecimalValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        if (cell.getCellType() == CellType.NUMERIC) {
            return BigDecimal.valueOf(cell.getNumericCellValue());
        } else if (cell.getCellType() == CellType.STRING) {
            String cellValue = cell.getStringCellValue().trim();
            if (cellValue.isEmpty()) {
                return null;
            }

            try {
                // 尝试直接转换为BigDecimal
                return new BigDecimal(cellValue);
            } catch (NumberFormatException e) {
                // 如果直接转换失败，尝试提取数字部分
                StringBuilder numericPart = new StringBuilder();
                boolean hasDecimalPoint = false;

                for (int i = 0; i < cellValue.length(); i++) {
                    char c = cellValue.charAt(i);
                    if (Character.isDigit(c)) {
                        numericPart.append(c);
                    } else if (c == '.' && !hasDecimalPoint) {
                        // 只允许一个小数点
                        numericPart.append(c);
                        hasDecimalPoint = true;
                    }
                }

                if (numericPart.length() > 0) {
                    try {
                        BigDecimal result = new BigDecimal(numericPart.toString());
                        log.info("从包含非数字字符的单元格值 [{}] 中提取数字部分 [{}]", cellValue, result);
                        return result;
                    } catch (NumberFormatException ex) {
                        log.warn("无法从单元格值 [{}] 中提取有效的小数", cellValue);
                        return null;
                    }
                } else {
                    log.warn("单元格值 [{}] 不包含任何数字", cellValue);
                    return null;
                }
            }
        } else {
            return null;
        }
    }

    /**
     * 获取单元格日期值
     */
    private static LocalDate getLocalDateValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        if (cell.getCellType() == CellType.NUMERIC && DateUtil.isCellDateFormatted(cell)) {
            return cell.getLocalDateTimeCellValue().toLocalDate();
        } else if (cell.getCellType() == CellType.STRING) {
            String cellValue = cell.getStringCellValue();

            // 如果包含星期几，先提取日期部分
            if (cellValue.contains("星期")) {
                String[] parts = cellValue.split(" ");
                if (parts.length > 0) {
                    cellValue = parts[0];
                }
            }

            try {
                // 尝试解析 yyyy-MM-dd 格式
                return LocalDate.parse(cellValue, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } catch (Exception e) {
                try {
                    // 尝试解析 yyyy/MM/dd 格式
                    return LocalDate.parse(cellValue, DateTimeFormatter.ofPattern("yyyy/MM/dd"));
                } catch (Exception e2) {
                    return null;
                }
            }
        } else {
            return null;
        }
    }

    /**
     * 获取单元格时间值
     */
    private static LocalTime getLocalTimeValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        if (cell.getCellType() == CellType.NUMERIC && DateUtil.isCellDateFormatted(cell)) {
            return cell.getLocalDateTimeCellValue().toLocalTime();
        } else if (cell.getCellType() == CellType.STRING) {
            try {
                return LocalTime.parse(cell.getStringCellValue(), DateTimeFormatter.ofPattern("HH:mm:ss"));
            } catch (Exception e) {
                try {
                    return LocalTime.parse(cell.getStringCellValue(), DateTimeFormatter.ofPattern("HH:mm"));
                } catch (Exception ex) {
                    return null;
                }
            }
        } else {
            return null;
        }
    }
}




