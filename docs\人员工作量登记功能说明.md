# 人员工作量登记功能说明

## 功能概述

人员工作量登记功能是一个完整的工作量管理系统，支持不同厂商的人员登记自己的月度工作量，管理者可以执行计算和审批操作。

## 1. 优化完成的功能

### ✅ 季度厂商功能多选样式优化

**优化内容**：
- 参考外包核算-执行核算的厂商选择样式
- 增强的多选下拉框功能
- 智能显示选中数量
- 支持搜索和全选/取消全选

**优化效果**：
```html
<select name="vendorIds" id="vendorIds" class="form-control selectpicker" multiple 
        data-live-search="true" 
        data-actions-box="true" 
        data-selected-text-format="count > 3" 
        data-count-selected-text="{0} 个厂商已选择"
        title="请选择厂商">
```

**JavaScript配置**：
```javascript
$('.selectpicker').selectpicker({
    noneSelectedText: '请选择厂商',
    selectAllText: '全选',
    deselectAllText: '取消全选',
    countSelectedText: function(numSelected, numTotal) {
        return numSelected + " 个厂商已选择";
    },
    liveSearch: true,
    actionsBox: true
});
```

## 2. 新增的人员工作量登记功能

### 📋 **核心功能模块**

#### 2.1 工作量登记管理
- **功能**：员工登记月度工作内容和工作天数
- **权限**：员工可以登记自己的工作量，管理者可以查看所有人的工作量
- **状态流转**：草稿 → 已提交 → 已计算 → 已审批

#### 2.2 人员单价配置
- **功能**：配置不同员工的人月单价
- **支持**：按生效日期管理单价变更
- **灵活性**：支持不同厂商、不同员工的差异化定价

#### 2.3 自动计算结算金额
- **计算公式**：人月数 = 工作天数 ÷ 厂商平均计薪工作日
- **结算金额**：人月数 × 人员单价
- **权限控制**：只有有权限的管理者可以执行计算

#### 2.4 工作量统计分析
- **统计维度**：按厂商、年月统计
- **统计内容**：总记录数、总工作天数、总人月数、总结算金额
- **状态分布**：各状态记录数量分布

### 🗄️ **数据库设计**

#### 主要数据表：

1. **staff_workload_record（工作量登记表）**
   - 记录员工月度工作量信息
   - 支持工作内容描述、工作天数、工作小时数
   - 自动计算人月数和结算金额
   - 完整的状态流转和审批流程

2. **staff_unit_price（人员单价配置表）**
   - 管理员工的人月单价
   - 支持按日期生效的价格管理
   - 支持价格历史记录

3. **sys_vendor（厂商表扩展）**
   - 新增 avg_work_days_per_month 字段
   - 用于配置厂商的平均计薪工作日（默认22天）

### 🔐 **权限设计**

#### 权限分级：
- **员工权限**：登记、修改自己的工作量（草稿和已提交状态）
- **管理者权限**：查看所有工作量、执行计算、审批
- **系统管理员**：配置人员单价、厂商参数

#### 权限列表：
```
system:workload:record:view     - 查看工作量登记
system:workload:record:add      - 新增工作量登记
system:workload:record:edit     - 修改工作量登记
system:workload:record:remove   - 删除工作量登记
system:workload:record:calculate - 计算结算金额
system:workload:record:approve  - 审批工作量
system:workload:price:*         - 单价配置相关权限
```

### 📊 **业务流程**

#### 标准工作流程：
1. **员工登记**：员工登记月度工作内容和工作天数
2. **提交审核**：员工提交工作量登记
3. **管理计算**：管理者执行结算金额计算
4. **审批确认**：管理者审批工作量登记
5. **数据统计**：生成各种统计报表

#### 计算逻辑：
```
人月数 = 工作天数 ÷ 厂商平均计薪工作日
结算金额 = 人月数 × 人员单价

示例：
- 工作天数：20天
- 厂商平均计薪工作日：22天
- 人员单价：25000元/人月
- 人月数：20 ÷ 22 = 0.9091人月
- 结算金额：0.9091 × 25000 = 22727.27元
```

### 🎨 **界面特性**

#### 用户体验优化：
- **智能表单**：自动设置当前年月为默认值
- **状态标识**：不同状态用不同颜色的标签显示
- **权限控制**：根据状态和权限动态显示操作按钮
- **数据验证**：前端和后端双重数据验证
- **批量操作**：支持批量提交、审批等操作

#### 响应式设计：
- 支持PC和移动端访问
- 表格自适应屏幕大小
- 模态框弹窗优化

### 📈 **统计功能**

#### 实时统计：
- **记录统计**：总记录数、各状态记录数
- **工作量统计**：总工作天数、总人月数
- **金额统计**：总结算金额
- **进度监控**：各状态的处理进度

#### 数据展示：
- 卡片式统计面板
- 状态分布表格
- 实时数据更新

### 🔧 **技术特性**

#### 后端技术：
- **Spring Boot**：主框架
- **MyBatis**：数据访问层
- **事务管理**：确保数据一致性
- **权限控制**：基于Shiro的细粒度权限

#### 前端技术：
- **Bootstrap**：响应式UI框架
- **jQuery**：JavaScript库
- **Bootstrap Select**：增强的下拉选择组件
- **Bootstrap Table**：数据表格组件

#### 数据库特性：
- **约束设计**：厂商+员工+年月唯一约束
- **索引优化**：针对查询场景的索引设计
- **数据完整性**：外键约束和数据验证

### 📋 **使用说明**

#### 员工操作流程：
1. 登录系统，进入"人员工作量登记"
2. 点击"添加"按钮，填写工作量信息
3. 保存为草稿，可以多次修改
4. 确认无误后点击"提交"

#### 管理者操作流程：
1. 查看员工提交的工作量登记
2. 点击"计算结算金额"，选择厂商和年月
3. 系统自动计算人月数和结算金额
4. 审核无误后点击"审批"确认

#### 配置管理：
1. 在"单价配置"中设置员工的人月单价
2. 在"厂商管理"中配置平均计薪工作日
3. 设置相关人员的操作权限

### 🎯 **业务价值**

#### 管理效益：
- **规范化管理**：标准化的工作量登记流程
- **自动化计算**：减少人工计算错误
- **透明化流程**：完整的审批和状态跟踪
- **数据化决策**：丰富的统计分析数据

#### 用户体验：
- **简化操作**：直观的界面和流程
- **移动友好**：支持移动端操作
- **实时反馈**：即时的状态更新和提示
- **权限保护**：安全的数据访问控制

这个功能完整地实现了您提出的需求，包括人员工作量登记、自动计算人月和结算金额、权限控制等核心功能，同时优化了季度厂商选择的用户体验。
