package com.timetrex.system.service;

import java.io.File;

/**
 * 邮件服务接口
 * 
 * <AUTHOR>
 * @date 2025-05-09
 */
public interface IEmailService 
{
    /**
     * 发送简单文本邮件
     * 
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     * @return 是否发送成功
     */
    public boolean sendSimpleMail(String to, String subject, String content);
    
    /**
     * 发送HTML邮件
     * 
     * @param to 收件人
     * @param subject 主题
     * @param content HTML内容
     * @return 是否发送成功
     */
    public boolean sendHtmlMail(String to, String subject, String content);
    
    /**
     * 发送带附件的邮件
     * 
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     * @param file 附件
     * @return 是否发送成功
     */
    public boolean sendAttachmentMail(String to, String subject, String content, File file);
    
    /**
     * 发送带附件的邮件
     * 
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     * @param filePath 附件路径
     * @return 是否发送成功
     */
    public boolean sendAttachmentMail(String to, String subject, String content, String filePath);
}
