-- 人员工作量登记表
CREATE TABLE staff_workload_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    vendor_id BIGINT NOT NULL COMMENT '厂商ID',
    vendor_name VARCHAR(100) NOT NULL COMMENT '厂商名称',
    staff_no VARCHAR(50) NOT NULL COMMENT '员工工号',
    staff_name VARCHAR(100) NOT NULL COMMENT '员工姓名',
    record_year INT NOT NULL COMMENT '登记年份',
    record_month INT NOT NULL COMMENT '登记月份',
    work_description TEXT COMMENT '工作内容描述',
    work_days DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '工作天数',
    work_hours DECIMAL(8,2) COMMENT '工作小时数',
    person_months DECIMAL(8,4) COMMENT '人月数（工作天数/平均计薪工作日）',
    unit_price DECIMAL(10,2) COMMENT '人员单价（元/人月）',
    settlement_amount DECIMAL(12,2) COMMENT '结算金额（人月数*单价）',
    status VARCHAR(20) DEFAULT 'DRAFT' COMMENT '状态：DRAFT-草稿，SUBMITTED-已提交，CALCULATED-已计算，APPROVED-已审批',
    submit_time DATETIME COMMENT '提交时间',
    calculate_time DATETIME COMMENT '计算时间',
    calculate_user VARCHAR(100) COMMENT '计算人员',
    approve_time DATETIME COMMENT '审批时间',
    approve_user VARCHAR(100) COMMENT '审批人员',
    remark TEXT COMMENT '备注',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    
    UNIQUE KEY uk_vendor_staff_month (vendor_id, staff_no, record_year, record_month) COMMENT '厂商+员工+年月唯一约束',
    INDEX idx_vendor_month (vendor_id, record_year, record_month) COMMENT '厂商月份索引',
    INDEX idx_staff (staff_no, staff_name) COMMENT '员工索引',
    INDEX idx_status (status) COMMENT '状态索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='人员工作量登记表';

-- 厂商管理表增加平均计薪工作日字段
ALTER TABLE sys_vendor ADD COLUMN avg_work_days_per_month DECIMAL(5,2) DEFAULT 22.00 COMMENT '平均计薪工作日/月';

-- 更新现有厂商的默认值
UPDATE sys_vendor SET avg_work_days_per_month = 22.00 WHERE avg_work_days_per_month IS NULL;

-- 人员单价配置表
CREATE TABLE staff_unit_price (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    vendor_id BIGINT NOT NULL COMMENT '厂商ID',
    staff_no VARCHAR(50) NOT NULL COMMENT '员工工号',
    staff_name VARCHAR(100) NOT NULL COMMENT '员工姓名',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '单价（元/人月）',
    effective_date DATE NOT NULL COMMENT '生效日期',
    expire_date DATE COMMENT '失效日期',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-有效，INACTIVE-无效',
    remark TEXT COMMENT '备注',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    
    UNIQUE KEY uk_vendor_staff_date (vendor_id, staff_no, effective_date) COMMENT '厂商+员工+生效日期唯一约束',
    INDEX idx_vendor (vendor_id) COMMENT '厂商索引',
    INDEX idx_staff (staff_no) COMMENT '员工索引',
    INDEX idx_effective_date (effective_date) COMMENT '生效日期索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='人员单价配置表';

-- 插入示例数据
INSERT INTO staff_unit_price (vendor_id, staff_no, staff_name, unit_price, effective_date, status, create_by) VALUES
(1, 'tech001', '张三', 25000.00, '2024-01-01', 'ACTIVE', 'admin'),
(1, 'tech002', '李四', 22000.00, '2024-01-01', 'ACTIVE', 'admin'),
(1, 'tech003', '王五', 28000.00, '2024-01-01', 'ACTIVE', 'admin'),
(2, 'dev001', '赵六', 26000.00, '2024-01-01', 'ACTIVE', 'admin'),
(2, 'dev002', '钱七', 24000.00, '2024-01-01', 'ACTIVE', 'admin');

-- 权限配置
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('人员工作量管理', 2000, 6, 'workload', NULL, 1, 0, 'M', '0', '0', NULL, 'fa fa-users', 'admin', NOW(), '', NULL, '人员工作量管理目录'),
('工作量登记', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '人员工作量管理') t), 1, 'record', 'system/workload/record/index', 1, 0, 'C', '0', '0', 'system:workload:record:view', 'fa fa-edit', 'admin', NOW(), '', NULL, '工作量登记菜单'),
('单价配置', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '人员工作量管理') t), 2, 'price', 'system/workload/price/index', 1, 0, 'C', '0', '0', 'system:workload:price:view', 'fa fa-money', 'admin', NOW(), '', NULL, '单价配置菜单'),
('工作量统计', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '人员工作量管理') t), 3, 'statistics', 'system/workload/statistics/index', 1, 0, 'C', '0', '0', 'system:workload:statistics:view', 'fa fa-bar-chart', 'admin', NOW(), '', NULL, '工作量统计菜单');

-- 工作量登记权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('工作量登记查询', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '工作量登记') t), 1, '', '', 1, 0, 'F', '0', '0', 'system:workload:record:query', '#', 'admin', NOW(), '', NULL, ''),
('工作量登记新增', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '工作量登记') t), 2, '', '', 1, 0, 'F', '0', '0', 'system:workload:record:add', '#', 'admin', NOW(), '', NULL, ''),
('工作量登记修改', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '工作量登记') t), 3, '', '', 1, 0, 'F', '0', '0', 'system:workload:record:edit', '#', 'admin', NOW(), '', NULL, ''),
('工作量登记删除', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '工作量登记') t), 4, '', '', 1, 0, 'F', '0', '0', 'system:workload:record:remove', '#', 'admin', NOW(), '', NULL, ''),
('工作量登记导出', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '工作量登记') t), 5, '', '', 1, 0, 'F', '0', '0', 'system:workload:record:export', '#', 'admin', NOW(), '', NULL, ''),
('工作量计算', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '工作量登记') t), 6, '', '', 1, 0, 'F', '0', '0', 'system:workload:record:calculate', '#', 'admin', NOW(), '', NULL, ''),
('工作量审批', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '工作量登记') t), 7, '', '', 1, 0, 'F', '0', '0', 'system:workload:record:approve', '#', 'admin', NOW(), '', NULL, '');

-- 单价配置权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('单价配置查询', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '单价配置') t), 1, '', '', 1, 0, 'F', '0', '0', 'system:workload:price:query', '#', 'admin', NOW(), '', NULL, ''),
('单价配置新增', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '单价配置') t), 2, '', '', 1, 0, 'F', '0', '0', 'system:workload:price:add', '#', 'admin', NOW(), '', NULL, ''),
('单价配置修改', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '单价配置') t), 3, '', '', 1, 0, 'F', '0', '0', 'system:workload:price:edit', '#', 'admin', NOW(), '', NULL, ''),
('单价配置删除', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '单价配置') t), 4, '', '', 1, 0, 'F', '0', '0', 'system:workload:price:remove', '#', 'admin', NOW(), '', NULL, ''),
('单价配置导出', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '单价配置') t), 5, '', '', 1, 0, 'F', '0', '0', 'system:workload:price:export', '#', 'admin', NOW(), '', NULL, '');
