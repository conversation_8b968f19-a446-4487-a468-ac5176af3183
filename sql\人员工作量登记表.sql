-- 人员工作量登记表
CREATE TABLE staff_workload_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    vendor_id BIGINT NOT NULL COMMENT '厂商ID',
    vendor_name VARCHAR(100) NOT NULL COMMENT '厂商名称',
    staff_no VARCHAR(50) NOT NULL COMMENT '员工工号',
    staff_name VARCHAR(100) NOT NULL COMMENT '员工姓名',
    record_year INT NOT NULL COMMENT '登记年份',
    record_month INT NOT NULL COMMENT '登记月份',
    work_description TEXT COMMENT '工作内容描述',
    work_days DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '工作天数',
    work_hours DECIMAL(8,2) COMMENT '工作小时数',
    person_months DECIMAL(8,4) COMMENT '人月数（工作天数/平均计薪工作日）',
    unit_price DECIMAL(10,2) COMMENT '人员单价（元/人月）',
    settlement_amount DECIMAL(12,2) COMMENT '结算金额（人月数*单价）',
    status VARCHAR(20) DEFAULT 'DRAFT' COMMENT '状态：DRAFT-草稿，SUBMITTED-已提交，CALCULATED-已计算，APPROVED-已审批',
    submit_time DATETIME COMMENT '提交时间',
    calculate_time DATETIME COMMENT '计算时间',
    calculate_user VARCHAR(100) COMMENT '计算人员',
    approve_time DATETIME COMMENT '审批时间',
    approve_user VARCHAR(100) COMMENT '审批人员',
    remark TEXT COMMENT '备注',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',

    UNIQUE KEY uk_vendor_staff_month (vendor_id, staff_no, record_year, record_month) COMMENT '厂商+员工+年月唯一约束',
    INDEX idx_vendor_month (vendor_id, record_year, record_month) COMMENT '厂商月份索引',
    INDEX idx_staff (staff_no, staff_name) COMMENT '员工索引',
    INDEX idx_status (status) COMMENT '状态索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='人员工作量登记表';

-- 注意：系统中已存在厂商管理表(vendor)，包含avg_work_days字段
-- 注意：系统中已存在人员管理表(staff_info)，包含vendor_id和unit_price字段
-- 因此不需要创建额外的单价配置表，直接使用现有数据结构

-- 权限配置
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('人员工作量管理', 2000, 6, 'workload', NULL, 1, 0, 'M', '0', '0', NULL, 'fa fa-users', 'admin', NOW(), '', NULL, '人员工作量管理目录'),
('工作量登记', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '人员工作量管理') t), 1, 'record', 'system/workload/record/index', 1, 0, 'C', '0', '0', 'system:workload:record:view', 'fa fa-edit', 'admin', NOW(), '', NULL, '工作量登记菜单'),
('工作量统计', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '人员工作量管理') t), 2, 'statistics', 'system/workload/statistics/statistics', 1, 0, 'C', '0', '0', 'system:workload:statistics:view', 'fa fa-bar-chart', 'admin', NOW(), '', NULL, '工作量统计菜单');

-- 工作量登记权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('工作量登记查询', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '工作量登记') t), 1, '', '', 1, 0, 'F', '0', '0', 'system:workload:record:query', '#', 'admin', NOW(), '', NULL, ''),
('工作量登记新增', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '工作量登记') t), 2, '', '', 1, 0, 'F', '0', '0', 'system:workload:record:add', '#', 'admin', NOW(), '', NULL, ''),
('工作量登记修改', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '工作量登记') t), 3, '', '', 1, 0, 'F', '0', '0', 'system:workload:record:edit', '#', 'admin', NOW(), '', NULL, ''),
('工作量登记删除', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '工作量登记') t), 4, '', '', 1, 0, 'F', '0', '0', 'system:workload:record:remove', '#', 'admin', NOW(), '', NULL, ''),
('工作量登记导出', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '工作量登记') t), 5, '', '', 1, 0, 'F', '0', '0', 'system:workload:record:export', '#', 'admin', NOW(), '', NULL, ''),
('工作量计算', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '工作量登记') t), 6, '', '', 1, 0, 'F', '0', '0', 'system:workload:record:calculate', '#', 'admin', NOW(), '', NULL, ''),
('工作量审批', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '工作量登记') t), 7, '', '', 1, 0, 'F', '0', '0', 'system:workload:record:approve', '#', 'admin', NOW(), '', NULL, '');

-- 工作量统计权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('工作量统计查询', (SELECT id FROM (SELECT id FROM sys_menu WHERE menu_name = '工作量统计') t), 1, '', '', 1, 0, 'F', '0', '0', 'system:workload:statistics:query', '#', 'admin', NOW(), '', NULL, '');
