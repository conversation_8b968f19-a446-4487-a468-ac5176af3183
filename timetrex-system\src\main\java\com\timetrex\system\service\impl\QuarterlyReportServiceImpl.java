package com.timetrex.system.service.impl;

import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.timetrex.common.config.TimeTrexConfig;
import com.timetrex.common.utils.poi.ExcelUtil;
import com.timetrex.system.domain.QuarterlyReport;
import com.timetrex.system.domain.SettlementResult;
import com.timetrex.system.domain.Vendor;
import com.timetrex.system.service.IQuarterlyReportService;
import com.timetrex.system.service.ISettlementResultService;
import com.timetrex.system.service.IVendorService;

/**
 * 季度归总报表Service实现
 * 
 * <AUTHOR>
 * @date 2025-05-15
 */
@Service
public class QuarterlyReportServiceImpl implements IQuarterlyReportService
{
    private static final Logger log = LoggerFactory.getLogger(QuarterlyReportServiceImpl.class);
    
    @Autowired
    private ISettlementResultService settlementResultService;
    
    @Autowired
    private IVendorService vendorService;

    /**
     * 生成季度归总报表
     * 
     * @param year 年份
     * @param quarter 季度（1-4）
     * @param vendorIds 厂商ID列表
     * @return 季度归总报表
     */
    @Override
    public List<QuarterlyReport> generateQuarterlyReport(int year, int quarter, List<Long> vendorIds)
    {
        log.info("开始生成季度归总报表，年份：{}，季度：{}，厂商ID：{}", year, quarter, vendorIds);
        
        // 获取季度的起止日期
        Map<String, LocalDate> dateRange = getQuarterDateRange(year, quarter);
        LocalDate startDate = dateRange.get("startDate");
        LocalDate endDate = dateRange.get("endDate");
        
        // 获取季度内的三个月份
        LocalDate month1 = startDate;
        LocalDate month2 = startDate.plusMonths(1);
        LocalDate month3 = startDate.plusMonths(2);
        
        List<QuarterlyReport> reportList = new ArrayList<>();
        
        try {
            // 处理每个厂商
            for (Long vendorId : vendorIds) {
                Vendor vendor = vendorService.selectVendorById(vendorId);
                if (vendor == null) {
                    log.warn("厂商不存在，厂商ID：{}", vendorId);
                    continue;
                }
                
                // 获取第一个月的结算结果
                List<SettlementResult> results1 = settlementResultService.selectSettlementResultByMonthAndVendor(month1, vendorId);
                
                // 获取第二个月的结算结果
                List<SettlementResult> results2 = settlementResultService.selectSettlementResultByMonthAndVendor(month2, vendorId);
                
                // 获取第三个月的结算结果
                List<SettlementResult> results3 = settlementResultService.selectSettlementResultByMonthAndVendor(month3, vendorId);
                
                // 合并三个月的结果，按员工姓名分组
                Map<String, List<SettlementResult>> staffResults1 = results1.stream()
                        .collect(Collectors.groupingBy(SettlementResult::getStaffName));
                
                Map<String, List<SettlementResult>> staffResults2 = results2.stream()
                        .collect(Collectors.groupingBy(SettlementResult::getStaffName));
                
                Map<String, List<SettlementResult>> staffResults3 = results3.stream()
                        .collect(Collectors.groupingBy(SettlementResult::getStaffName));
                
                // 获取所有员工姓名
                List<String> allStaffNames = new ArrayList<>();
                allStaffNames.addAll(staffResults1.keySet());
                allStaffNames.addAll(staffResults2.keySet());
                allStaffNames.addAll(staffResults3.keySet());
                allStaffNames = allStaffNames.stream().distinct().collect(Collectors.toList());
                
                // 为每个员工生成季度报表
                for (String staffName : allStaffNames) {
                    QuarterlyReport report = new QuarterlyReport();
                    report.setStaffName(staffName);
                    report.setVendorId(vendorId);
                    report.setVendorName(vendor.getName());
                    report.setYear(year);
                    report.setQuarter(quarter);
                    
                    // 设置第一个月的数据
                    if (staffResults1.containsKey(staffName)) {
                        SettlementResult result = staffResults1.get(staffName).get(0);
                        report.setStandardHours1(result.getStandardHours());
                        report.setCalculatedHours1(result.getCalculatedHours());
                        report.setWorkDays1(result.getWorkDays());
                        report.setAmount1(result.getAmount());
                    } else {
                        report.setStandardHours1(BigDecimal.ZERO);
                        report.setCalculatedHours1(BigDecimal.ZERO);
                        report.setWorkDays1(BigDecimal.ZERO);
                        report.setAmount1(BigDecimal.ZERO);
                    }
                    
                    // 设置第二个月的数据
                    if (staffResults2.containsKey(staffName)) {
                        SettlementResult result = staffResults2.get(staffName).get(0);
                        report.setStandardHours2(result.getStandardHours());
                        report.setCalculatedHours2(result.getCalculatedHours());
                        report.setWorkDays2(result.getWorkDays());
                        report.setAmount2(result.getAmount());
                    } else {
                        report.setStandardHours2(BigDecimal.ZERO);
                        report.setCalculatedHours2(BigDecimal.ZERO);
                        report.setWorkDays2(BigDecimal.ZERO);
                        report.setAmount2(BigDecimal.ZERO);
                    }
                    
                    // 设置第三个月的数据
                    if (staffResults3.containsKey(staffName)) {
                        SettlementResult result = staffResults3.get(staffName).get(0);
                        report.setStandardHours3(result.getStandardHours());
                        report.setCalculatedHours3(result.getCalculatedHours());
                        report.setWorkDays3(result.getWorkDays());
                        report.setAmount3(result.getAmount());
                    } else {
                        report.setStandardHours3(BigDecimal.ZERO);
                        report.setCalculatedHours3(BigDecimal.ZERO);
                        report.setWorkDays3(BigDecimal.ZERO);
                        report.setAmount3(BigDecimal.ZERO);
                    }
                    
                    // 计算季度总和
                    report.setTotalStandardHours(
                            report.getStandardHours1().add(report.getStandardHours2()).add(report.getStandardHours3()));
                    report.setTotalCalculatedHours(
                            report.getCalculatedHours1().add(report.getCalculatedHours2()).add(report.getCalculatedHours3()));
                    report.setTotalWorkDays(
                            report.getWorkDays1().add(report.getWorkDays2()).add(report.getWorkDays3()));
                    report.setTotalAmount(
                            report.getAmount1().add(report.getAmount2()).add(report.getAmount3()));
                    
                    reportList.add(report);
                }
            }
            
            log.info("季度归总报表生成完成，共 {} 条记录", reportList.size());
            return reportList;
        } catch (Exception e) {
            log.error("生成季度归总报表失败", e);
            throw new RuntimeException("生成季度归总报表失败：" + e.getMessage(), e);
        }
    }

    /**
     * 获取季度的起止日期
     * 
     * @param year 年份
     * @param quarter 季度（1-4）
     * @return 包含起始日期和结束日期的Map
     */
    @Override
    public Map<String, LocalDate> getQuarterDateRange(int year, int quarter)
    {
        Map<String, LocalDate> result = new HashMap<>();
        
        switch (quarter) {
            case 1:
                result.put("startDate", LocalDate.of(year, 1, 1));
                result.put("endDate", LocalDate.of(year, 3, 31));
                break;
            case 2:
                result.put("startDate", LocalDate.of(year, 4, 1));
                result.put("endDate", LocalDate.of(year, 6, 30));
                break;
            case 3:
                result.put("startDate", LocalDate.of(year, 7, 1));
                result.put("endDate", LocalDate.of(year, 9, 30));
                break;
            case 4:
                result.put("startDate", LocalDate.of(year, 10, 1));
                result.put("endDate", LocalDate.of(year, 12, 31));
                break;
            default:
                throw new IllegalArgumentException("季度必须是1-4之间的整数");
        }
        
        return result;
    }

    /**
     * 导出季度归总报表
     * 
     * @param year 年份
     * @param quarter 季度（1-4）
     * @param vendorIds 厂商ID列表
     * @return 导出文件路径
     */
    @Override
    public String exportQuarterlyReport(int year, int quarter, List<Long> vendorIds)
    {
        try {
            // 生成季度归总报表
            List<QuarterlyReport> reportList = generateQuarterlyReport(year, quarter, vendorIds);
            
            // 获取厂商名称
            List<String> vendorNames = new ArrayList<>();
            for (Long vendorId : vendorIds) {
                Vendor vendor = vendorService.selectVendorById(vendorId);
                if (vendor != null) {
                    vendorNames.add(vendor.getName());
                }
            }
            
            // 生成文件名
            String vendorName = String.join("_", vendorNames);
            if (vendorName.length() > 50) {
                vendorName = vendorName.substring(0, 50) + "...";
            }
            
            String fileName = vendorName + "_" + year + "年第" + quarter + "季度_归总报表.xlsx";
            String filePath = TimeTrexConfig.getProfile() + "/" + fileName;
            
            // 使用ExcelUtil导出
            ExcelUtil<QuarterlyReport> util = new ExcelUtil<>(QuarterlyReport.class);
            util.exportExcel(reportList, "季度归总报表", filePath);
            
            return filePath;
        } catch (Exception e) {
            log.error("导出季度归总报表失败", e);
            throw new RuntimeException("导出季度归总报表失败：" + e.getMessage(), e);
        }
    }
}
