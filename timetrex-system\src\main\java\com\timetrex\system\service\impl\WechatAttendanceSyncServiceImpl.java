package com.timetrex.system.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.timetrex.common.utils.http.HttpUtils;
import com.timetrex.system.domain.AttendanceDetail;
import com.timetrex.system.domain.AttendanceSummary;
import com.timetrex.system.service.IAttendanceDetailService;
import com.timetrex.system.service.IAttendanceSummaryService;
import com.timetrex.system.service.IWechatAttendanceSyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.http.MediaType;

import java.math.BigDecimal;
import java.time.*;
import java.util.*;

@Service
public class WechatAttendanceSyncServiceImpl implements IWechatAttendanceSyncService {

    private static final Logger log = LoggerFactory.getLogger(WechatAttendanceSyncServiceImpl.class);

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Value("${wechat.corpid}")
    private String corpId;

    @Value("${wechat.checkin.secret}")
    private String checkinSecret;

    @Autowired
    private IAttendanceSummaryService attendanceSummaryService;

    @Autowired
    private IAttendanceDetailService attendanceDetailService;

    /**
     * 同步指定月份的打卡数据
     *
     * @param yearMonth 年月，格式为yyyy-MM
     * @return 同步结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> syncMonthlyAttendance(String yearMonth) {
        Map<String, Object> result = new HashMap<>();
        List<String> errorMessages = new ArrayList<>();

        try {
            // 解析年月
            YearMonth ym = YearMonth.parse(yearMonth);
            LocalDate startDate = ym.atDay(1);
            LocalDate endDate = ym.atEndOfMonth();

            log.info("开始同步企业微信打卡数据，时间范围：{} 至 {}", startDate, endDate);

            // 获取访问令牌
            String accessToken = getAccessToken();
            if (accessToken == null) {
                errorMessages.add("获取企业微信访问令牌失败");
                result.put("success", false);
                result.put("errorMessages", errorMessages);
                return result;
            }

            // 获取打卡日报数据
            List<Map<String, Object>> dayDataList = getCheckinDayData(accessToken, startDate, endDate);
            if (dayDataList == null) {
                errorMessages.add("获取企业微信打卡日报数据失败");
                result.put("success", false);
                result.put("errorMessages", errorMessages);
                return result;
            }

            // 获取打卡详情数据
            List<Map<String, Object>> checkinDataList = getCheckinData(accessToken, startDate, endDate);
            if (checkinDataList == null) {
                errorMessages.add("获取企业微信打卡详情数据失败");
                result.put("success", false);
                result.put("errorMessages", errorMessages);
                return result;
            }

            // 转换为系统数据格式
            List<AttendanceSummary> summaryList = convertToSummaryList(dayDataList);
            List<AttendanceDetail> detailList = convertToDetailList(checkinDataList);

            // 保存到数据库
            attendanceSummaryService.batchSave(summaryList);
            attendanceDetailService.batchSave(detailList);

            result.put("success", true);
            result.put("summaryCount", summaryList.size());
            result.put("detailCount", detailList.size());

            log.info("企业微信打卡数据同步完成，共同步概况数据 {} 条，详情数据 {} 条",
                    summaryList.size(), detailList.size());

        } catch (Exception e) {
            log.error("同步企业微信打卡数据失败", e);
            errorMessages.add("同步失败：" + e.getMessage());
            result.put("success", false);
            result.put("errorMessages", errorMessages);
        }

        return result;
    }

    /**
     * 获取企业微信访问令牌
     */
    private String getAccessToken() {
        try {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=" + corpId + "&corpsecret=" + checkinSecret;
            String response = HttpUtils.sendGet(url);

            log.debug("获取访问令牌响应: {}", response);

            // 解析JSON响应
            JsonNode jsonNode = objectMapper.readTree(response);
            int errcode = jsonNode.get("errcode").asInt();

            if (errcode == 0) {
                String accessToken = jsonNode.get("access_token").asText();
                log.info("成功获取企业微信访问令牌");
                return accessToken;
            } else {
                String errmsg = jsonNode.get("errmsg").asText();
                log.error("获取企业微信访问令牌失败，错误码: {}, 错误信息: {}", errcode, errmsg);
                return null;
            }
        } catch (Exception e) {
            log.error("获取企业微信访问令牌失败", e);
            return null;
        }
    }

    /**
     * 获取打卡日报数据
     */
    private List<Map<String, Object>> getCheckinDayData(String accessToken, LocalDate startDate, LocalDate endDate) {
        try {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/checkin/getcheckin_daydata?access_token=" + accessToken;

            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("starttime", startDate.atStartOfDay().toEpochSecond(java.time.ZoneOffset.of("+8")));
            params.put("endtime", endDate.plusDays(1).atStartOfDay().minusSeconds(1).toEpochSecond(java.time.ZoneOffset.of("+8")));
            params.put("useridlist", getUserIdList());

            // 将参数转换为JSON字符串
            String jsonParams = objectMapper.writeValueAsString(params);

            // 发送POST请求
            String response = HttpUtils.sendPost(url, jsonParams, MediaType.APPLICATION_JSON_VALUE);

            log.debug("获取打卡日报数据响应: {}", response);

            // 解析JSON响应
            JsonNode jsonNode = objectMapper.readTree(response);
            int errcode = jsonNode.get("errcode").asInt();

            if (errcode == 0) {
                List<Map<String, Object>> dayDataList = new ArrayList<>();
                JsonNode dataNode = jsonNode.get("datas");
                if (dataNode != null && dataNode.isArray()) {
                    for (JsonNode item : dataNode) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> dayData = objectMapper.convertValue(item, Map.class);
                        dayDataList.add(dayData);
                    }
                }
                log.info("成功获取企业微信打卡日报数据，数量: {}", dayDataList.size());
                return dayDataList;
            } else {
                String errmsg = jsonNode.get("errmsg").asText();
                log.error("获取企业微信打卡日报数据失败，错误码: {}, 错误信息: {}", errcode, errmsg);
                return null;
            }
        } catch (Exception e) {
            log.error("获取企业微信打卡日报数据失败", e);
            return null;
        }
    }

    /**
     * 获取打卡详情数据
     */
    private List<Map<String, Object>> getCheckinData(String accessToken, LocalDate startDate, LocalDate endDate) {
        try {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/checkin/getcheckindata?access_token=" + accessToken;

            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("starttime", startDate.atStartOfDay().toEpochSecond(java.time.ZoneOffset.of("+8")));
            params.put("endtime", endDate.plusDays(1).atStartOfDay().minusSeconds(1).toEpochSecond(java.time.ZoneOffset.of("+8")));
            params.put("useridlist", getUserIdList());

            // 将参数转换为JSON字符串
            String jsonParams = objectMapper.writeValueAsString(params);

            // 发送POST请求
            String response = HttpUtils.sendPost(url, jsonParams, MediaType.APPLICATION_JSON_VALUE);

            log.debug("获取打卡详情数据响应: {}", response);

            // 解析JSON响应
            JsonNode jsonNode = objectMapper.readTree(response);
            int errcode = jsonNode.get("errcode").asInt();

            if (errcode == 0) {
                List<Map<String, Object>> checkinDataList = new ArrayList<>();
                JsonNode dataNode = jsonNode.get("checkindata");
                if (dataNode != null && dataNode.isArray()) {
                    for (JsonNode item : dataNode) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> checkinData = objectMapper.convertValue(item, Map.class);
                        checkinDataList.add(checkinData);
                    }
                }
                log.info("成功获取企业微信打卡详情数据，数量: {}", checkinDataList.size());
                return checkinDataList;
            } else {
                String errmsg = jsonNode.get("errmsg").asText();
                log.error("获取企业微信打卡详情数据失败，错误码: {}, 错误信息: {}", errcode, errmsg);
                return null;
            }
        } catch (Exception e) {
            log.error("获取企业微信打卡详情数据失败", e);
            return null;
        }
    }

    /**
     * 获取需要同步的用户ID列表
     */
    private List<String> getUserIdList() {
        // TODO: 这里应该从数据库中查询需要同步的用户列表
        // 或者调用企业微信API获取部门用户列表
        // 暂时返回空列表，表示获取所有用户的打卡数据
        return new ArrayList<>();
    }

    /**
     * 将企业微信打卡日报数据转换为系统概况数据
     */
    private List<AttendanceSummary> convertToSummaryList(List<Map<String, Object>> dayDataList) {
        List<AttendanceSummary> summaryList = new ArrayList<>();

        for (Map<String, Object> dayData : dayDataList) {
            try {
                AttendanceSummary summary = new AttendanceSummary();

                // 基本信息
                summary.setStaffNo(getStringValue(dayData, "userid"));
                summary.setName(getStringValue(dayData, "name"));
                summary.setDepartment(getStringValue(dayData, "department"));
                summary.setPosition(getStringValue(dayData, "position"));

                // 日期信息 - 企微返回的是时间戳，需要转换
                Long dateTimestamp = getLongValue(dayData, "date");
                if (dateTimestamp != null) {
                    LocalDate recordDate = Instant.ofEpochSecond(dateTimestamp)
                            .atZone(ZoneId.of("Asia/Shanghai"))
                            .toLocalDate();
                    summary.setRecordDate(recordDate);

                    // 设置星期几
                    summary.setDayOfWeek(recordDate.getDayOfWeek().getDisplayName(
                            java.time.format.TextStyle.FULL, java.util.Locale.CHINA));
                }

                // 打卡时间信息 - 企微返回的是时间戳，需要转换为LocalTime
                Long earliestTimestamp = getLongValue(dayData, "earliest_time");
                if (earliestTimestamp != null && earliestTimestamp > 0) {
                    summary.setEarliestCheck(Instant.ofEpochSecond(earliestTimestamp)
                            .atZone(ZoneId.of("Asia/Shanghai"))
                            .toLocalTime());
                }

                Long latestTimestamp = getLongValue(dayData, "latest_time");
                if (latestTimestamp != null && latestTimestamp > 0) {
                    summary.setLatestCheck(Instant.ofEpochSecond(latestTimestamp)
                            .atZone(ZoneId.of("Asia/Shanghai"))
                            .toLocalTime());
                }

                // 打卡次数
                summary.setCheckCount(getIntegerValue(dayData, "checkin_count"));

                // 工作时长信息
                summary.setStandardHours(getBigDecimalValue(dayData, "standard_work_sec", 3600)); // 秒转小时
                summary.setActualHours(getBigDecimalValue(dayData, "work_sec", 3600)); // 秒转小时

                // 计算按9小时算的工作时长
                BigDecimal actualHours = summary.getActualHours();
                if (actualHours != null) {
                    BigDecimal calculatedHours = actualHours.min(new BigDecimal("9"));
                    summary.setCalculatedHours(calculatedHours);
                }

                // 设置默认值
                summary.setWorkRule("默认规则");
                summary.setShift("默认班次");

                summaryList.add(summary);

            } catch (Exception e) {
                log.error("转换企微日报数据失败: {}", dayData, e);
            }
        }

        return summaryList;
    }

    /**
     * 将企业微信打卡详情数据转换为系统详情数据
     */
    private List<AttendanceDetail> convertToDetailList(List<Map<String, Object>> checkinDataList) {
        List<AttendanceDetail> detailList = new ArrayList<>();

        for (Map<String, Object> checkinData : checkinDataList) {
            try {
                AttendanceDetail detail = new AttendanceDetail();

                // 基本信息
                detail.setStaffNo(getStringValue(checkinData, "userid"));
                detail.setName(getStringValue(checkinData, "name"));
                detail.setDepartment(getStringValue(checkinData, "department"));
                detail.setPosition(getStringValue(checkinData, "position"));

                // 打卡日期 - 企微返回的是时间戳，需要转换
                Long checkinTimestamp = getLongValue(checkinData, "checkin_time");
                if (checkinTimestamp != null) {
                    ZonedDateTime checkinDateTime = Instant.ofEpochSecond(checkinTimestamp)
                            .atZone(ZoneId.of("Asia/Shanghai"));
                    detail.setCheckDate(checkinDateTime.toLocalDate());
                    detail.setActualTime(checkinDateTime.toLocalTime());

                    // 设置星期几
                    detail.setDayOfWeek(checkinDateTime.toLocalDate().getDayOfWeek().getDisplayName(
                            java.time.format.TextStyle.FULL, java.util.Locale.CHINA));
                }

                // 打卡类型
                String checkinType = getStringValue(checkinData, "checkin_type");
                if ("上班".equals(checkinType) || "1".equals(checkinType)) {
                    detail.setCheckType("上班");
                } else if ("下班".equals(checkinType) || "2".equals(checkinType)) {
                    detail.setCheckType("下班");
                } else {
                    detail.setCheckType(checkinType);
                }

                // 打卡状态
                String exception = getStringValue(checkinData, "exception_type");
                if (exception == null || exception.isEmpty() || "无异常".equals(exception)) {
                    detail.setCheckStatus("正常");
                } else {
                    detail.setCheckStatus(exception);
                }

                // 打卡地点
                detail.setLocation(getStringValue(checkinData, "location_title"));

                // 设备信息
                detail.setDeviceId(getStringValue(checkinData, "deviceid"));

                // 备注信息
                detail.setRemark(getStringValue(checkinData, "notes"));

                // 设置默认值
                detail.setWorkRule("默认规则");
                detail.setScheduledTime(LocalTime.of(9, 0)); // 默认应打卡时间

                detailList.add(detail);

            } catch (Exception e) {
                log.error("转换企微打卡详情数据失败: {}", checkinData, e);
            }
        }

        return detailList;
    }

    /**
     * 从Map中获取字符串值
     */
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 从Map中获取Long值
     */
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 从Map中获取Integer值
     */
    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 从Map中获取BigDecimal值，并进行单位转换
     */
    private BigDecimal getBigDecimalValue(Map<String, Object> map, String key, int divisor) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        try {
            BigDecimal bigDecimalValue;
            if (value instanceof Number) {
                bigDecimalValue = new BigDecimal(value.toString());
            } else {
                bigDecimalValue = new BigDecimal(value.toString());
            }

            if (divisor > 1) {
                return bigDecimalValue.divide(new BigDecimal(divisor), 2, BigDecimal.ROUND_HALF_UP);
            }
            return bigDecimalValue;
        } catch (NumberFormatException e) {
            return null;
        }
    }
}