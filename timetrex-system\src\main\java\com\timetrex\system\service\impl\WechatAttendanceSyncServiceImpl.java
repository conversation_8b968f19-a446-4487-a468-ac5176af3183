package com.timetrex.system.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.timetrex.common.utils.http.HttpUtils;
import com.timetrex.system.config.WechatConfig;
import com.timetrex.system.domain.AttendanceDetail;
import com.timetrex.system.domain.AttendanceSummary;
import com.timetrex.system.service.IAttendanceDetailService;
import com.timetrex.system.service.IAttendanceSummaryService;
import com.timetrex.system.service.IWechatAttendanceSyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.http.MediaType;

import java.math.BigDecimal;
import java.time.*;
import java.util.*;

@Service
public class WechatAttendanceSyncServiceImpl implements IWechatAttendanceSyncService {

    private static final Logger log = LoggerFactory.getLogger(WechatAttendanceSyncServiceImpl.class);

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private WechatConfig wechatConfig;

    @Autowired
    private IAttendanceSummaryService attendanceSummaryService;

    @Autowired
    private IAttendanceDetailService attendanceDetailService;

    /**
     * 同步指定月份的打卡数据
     *
     * @param yearMonth 年月，格式为yyyy-MM
     * @return 同步结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> syncMonthlyAttendance(String yearMonth) {
        Map<String, Object> result = new HashMap<>();
        List<String> errorMessages = new ArrayList<>();

        try {
            // 解析年月，确保时间范围是每月1号到月底
            YearMonth ym = YearMonth.parse(yearMonth);
            LocalDate startDate = ym.atDay(1); // 每月1号
            LocalDate endDate = ym.atEndOfMonth(); // 每月最后一天

            log.info("开始同步企业微信打卡数据，时间范围：{} 至 {} (每月1号到月底)", startDate, endDate);
            log.info("时间戳范围：{} 至 {}",
                startDate.atStartOfDay().toEpochSecond(ZoneOffset.of("+8")),
                endDate.atTime(23, 59, 59).toEpochSecond(ZoneOffset.of("+8")));

            // 获取访问令牌
            String accessToken = getAccessToken();
            if (accessToken == null) {
                errorMessages.add("获取企业微信访问令牌失败");
                result.put("success", false);
                result.put("errorMessages", errorMessages);
                return result;
            }

            // 获取打卡日报数据
            List<Map<String, Object>> dayDataList = getCheckinDayData(accessToken, startDate, endDate);
            if (dayDataList == null) {
                errorMessages.add("获取企业微信打卡日报数据失败");
                result.put("success", false);
                result.put("errorMessages", errorMessages);
                return result;
            }

            // 获取打卡详情数据
            List<Map<String, Object>> checkinDataList = getCheckinData(accessToken, startDate, endDate);
            if (checkinDataList == null) {
                errorMessages.add("获取企业微信打卡详情数据失败");
                result.put("success", false);
                result.put("errorMessages", errorMessages);
                return result;
            }

            // 转换为系统数据格式
            List<AttendanceSummary> summaryList = convertToSummaryList(dayDataList);
            List<AttendanceDetail> detailList = convertToDetailList(checkinDataList);

            // 分批保存到数据库（性能优化）
            int summaryCount = batchSaveWithOptimization(summaryList, detailList);

            result.put("success", true);
            result.put("summaryCount", summaryList.size());
            result.put("detailCount", detailList.size());
            result.put("actualSavedCount", summaryCount);

            log.info("企业微信打卡数据同步完成，共同步概况数据 {} 条，详情数据 {} 条",
                    summaryList.size(), detailList.size());

        } catch (Exception e) {
            log.error("同步企业微信打卡数据失败", e);
            errorMessages.add("同步失败：" + e.getMessage());
            result.put("success", false);
            result.put("errorMessages", errorMessages);
        }

        return result;
    }

    /**
     * 获取企业微信访问令牌
     */
    private String getAccessToken() {
        try {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=" + wechatConfig.getCorpid() + "&corpsecret=" + wechatConfig.getCheckin().getSecret();
            String response = HttpUtils.sendGet(url);

            log.debug("获取访问令牌响应: {}", response);

            // 解析JSON响应
            JsonNode jsonNode = objectMapper.readTree(response);
            int errcode = jsonNode.get("errcode").asInt();

            if (errcode == 0) {
                String accessToken = jsonNode.get("access_token").asText();
                log.info("成功获取企业微信访问令牌");
                return accessToken;
            } else {
                String errmsg = jsonNode.get("errmsg").asText();
                log.error("获取企业微信访问令牌失败，错误码: {}, 错误信息: {}", errcode, errmsg);
                return null;
            }
        } catch (Exception e) {
            log.error("获取企业微信访问令牌失败", e);
            return null;
        }
    }

    /**
     * 获取打卡日报数据
     */
    private List<Map<String, Object>> getCheckinDayData(String accessToken, LocalDate startDate, LocalDate endDate) {
        try {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/checkin/getcheckin_daydata?access_token=" + accessToken;

            // 构建请求参数 - 确保时间范围是每月1号00:00:00到月底23:59:59
            Map<String, Object> params = new HashMap<>();
            // 每月1号00:00:00的时间戳
            params.put("starttime", startDate.atStartOfDay().toEpochSecond(ZoneOffset.of("+8")));
            // 每月最后一天23:59:59的时间戳
            params.put("endtime", endDate.atTime(23, 59, 59).toEpochSecond(ZoneOffset.of("+8")));
            params.put("useridlist", getUserIdList());

            // 将参数转换为JSON字符串
            String jsonParams = objectMapper.writeValueAsString(params);

            // 发送POST请求
            String response = HttpUtils.sendPost(url, jsonParams, MediaType.APPLICATION_JSON_VALUE);

            log.debug("获取打卡日报数据响应: {}", response);

            // 解析JSON响应
            JsonNode jsonNode = objectMapper.readTree(response);
            int errcode = jsonNode.get("errcode").asInt();

            if (errcode == 0) {
                List<Map<String, Object>> dayDataList = new ArrayList<>();
                JsonNode dataNode = jsonNode.get("datas");
                if (dataNode != null && dataNode.isArray()) {
                    for (JsonNode item : dataNode) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> dayData = objectMapper.convertValue(item, Map.class);
                        dayDataList.add(dayData);
                    }
                }
                log.info("成功获取企业微信打卡日报数据，数量: {}", dayDataList.size());
                return dayDataList;
            } else {
                String errmsg = jsonNode.get("errmsg").asText();
                log.error("获取企业微信打卡日报数据失败，错误码: {}, 错误信息: {}", errcode, errmsg);
                return null;
            }
        } catch (Exception e) {
            log.error("获取企业微信打卡日报数据失败", e);
            return null;
        }
    }

    /**
     * 获取打卡详情数据
     */
    private List<Map<String, Object>> getCheckinData(String accessToken, LocalDate startDate, LocalDate endDate) {
        try {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/checkin/getcheckindata?access_token=" + accessToken;

            // 构建请求参数 - 确保时间范围是每月1号00:00:00到月底23:59:59
            Map<String, Object> params = new HashMap<>();
            params.put("opencheckindatatype", 3);
            // 每月1号00:00:00的时间戳
            params.put("starttime", startDate.atStartOfDay().toEpochSecond(ZoneOffset.of("+8")));
            // 每月最后一天23:59:59的时间戳
            params.put("endtime", endDate.atTime(23, 59, 59).toEpochSecond(ZoneOffset.of("+8")));
            params.put("useridlist", getUserIdList());

            // 将参数转换为JSON字符串
            String jsonParams = objectMapper.writeValueAsString(params);

            // 发送POST请求
            String response = HttpUtils.sendPost(url, jsonParams, MediaType.APPLICATION_JSON_VALUE);

            log.debug("获取打卡详情数据响应: {}", response);

            // 解析JSON响应
            JsonNode jsonNode = objectMapper.readTree(response);
            int errcode = jsonNode.get("errcode").asInt();

            if (errcode == 0) {
                List<Map<String, Object>> checkinDataList = new ArrayList<>();
                JsonNode dataNode = jsonNode.get("checkindata");
                if (dataNode != null && dataNode.isArray()) {
                    for (JsonNode item : dataNode) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> checkinData = objectMapper.convertValue(item, Map.class);
                        checkinDataList.add(checkinData);
                    }
                }
                log.info("成功获取企业微信打卡详情数据，数量: {}", checkinDataList.size());
                return checkinDataList;
            } else {
                String errmsg = jsonNode.get("errmsg").asText();
                log.error("获取企业微信打卡详情数据失败，错误码: {}, 错误信息: {}", errcode, errmsg);
                return null;
            }
        } catch (Exception e) {
            log.error("获取企业微信打卡详情数据失败", e);
            return null;
        }
    }

    /**
     * 获取需要同步的用户ID列表
     */
    private List<String> getUserIdList() {
        // 从配置文件中读取用户ID列表
        List<String> userIds = wechatConfig.getSync().getUserIds();
        if (userIds != null && !userIds.isEmpty()) {
            log.info("从配置文件读取到 {} 个用户ID", userIds.size());
            return userIds;
        }

        // 如果配置为空，返回空列表（企微API会返回所有用户数据）
        log.warn("配置文件中未配置用户ID列表，将获取所有用户的打卡数据");
        return new ArrayList<>();
    }

    /**
     * 分批保存数据到数据库（性能优化）
     */
    @Transactional(rollbackFor = Exception.class)
    private int batchSaveWithOptimization(List<AttendanceSummary> summaryList, List<AttendanceDetail> detailList) {
        int totalSaved = 0;
        int dbBatchSize = wechatConfig.getSync().getDbBatchSize();

        log.info("开始分批保存数据，概况数据: {} 条，详情数据: {} 条，批次大小: {}",
                summaryList.size(), detailList.size(), dbBatchSize);

        try {
            // 分批保存概况数据
            if (!summaryList.isEmpty()) {
                totalSaved += batchSaveList(summaryList, dbBatchSize, "概况数据",
                    batch -> attendanceSummaryService.batchSave(batch));
            }

            // 分批保存详情数据
            if (!detailList.isEmpty()) {
                totalSaved += batchSaveList(detailList, dbBatchSize, "详情数据",
                    batch -> attendanceDetailService.batchSave(batch));
            }

            log.info("数据保存完成，总计保存: {} 条", totalSaved);
            return totalSaved;

        } catch (Exception e) {
            log.error("分批保存数据失败", e);
            throw e;
        }
    }

    /**
     * 通用分批保存方法
     */
    private <T> int batchSaveList(List<T> dataList, int batchSize, String dataType,
                                  java.util.function.Function<List<T>, Integer> saveFunction) {
        int totalSaved = 0;
        int totalBatches = (int) Math.ceil((double) dataList.size() / batchSize);

        log.info("开始分批保存{}，总数: {} 条，分 {} 批处理", dataType, dataList.size(), totalBatches);

        for (int i = 0; i < dataList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, dataList.size());
            List<T> batch = dataList.subList(i, endIndex);

            try {
                int saved = saveFunction.apply(batch);
                totalSaved += saved;

                int currentBatch = (i / batchSize) + 1;
                log.info("{}第 {}/{} 批保存完成，本批: {} 条，累计: {} 条",
                        dataType, currentBatch, totalBatches, saved, totalSaved);

                // 添加短暂延迟，避免数据库压力过大
                if (currentBatch < totalBatches) {
                    Thread.sleep(100); // 100ms延迟
                }

            } catch (Exception e) {
                log.error("{}第 {} 批保存失败，批次大小: {}", dataType, (i / batchSize) + 1, batch.size(), e);
                throw new RuntimeException("分批保存" + dataType + "失败", e);
            }
        }

        return totalSaved;
    }

    /**
     * 将企业微信打卡日报数据转换为系统概况数据
     */
    private List<AttendanceSummary> convertToSummaryList(List<Map<String, Object>> dayDataList) {
        List<AttendanceSummary> summaryList = new ArrayList<>();

        for (Map<String, Object> dayData : dayDataList) {
            try {
                AttendanceSummary summary = new AttendanceSummary();

                // 获取base_info信息
                @SuppressWarnings("unchecked")
                Map<String, Object> baseInfo = (Map<String, Object>) dayData.get("base_info");
                if (baseInfo == null) {
                    log.warn("dayData中缺少base_info信息: {}", dayData);
                    continue;
                }

                // 获取summary_info信息
                @SuppressWarnings("unchecked")
                Map<String, Object> summaryInfo = (Map<String, Object>) dayData.get("summary_info");
                if (summaryInfo == null) {
                    log.warn("dayData中缺少summary_info信息: {}", dayData);
                    continue;
                }

                // 基本信息 - 从base_info中获取
                summary.setStaffNo(getStringValue(baseInfo, "acctid"));
                summary.setName(getStringValue(baseInfo, "name"));
                summary.setDepartment(getStringValue(baseInfo, "departs_name"));

                // 获取规则信息
                @SuppressWarnings("unchecked")
                Map<String, Object> ruleInfo = (Map<String, Object>) baseInfo.get("rule_info");
                if (ruleInfo != null) {
                    summary.setWorkRule(getStringValue(ruleInfo, "groupname"));
                    summary.setShift(getStringValue(ruleInfo, "schedulename"));
                }

                // 日期信息 - 企微返回的是时间戳，需要转换
                Long dateTimestamp = getLongValue(baseInfo, "date");
                if (dateTimestamp != null) {
                    LocalDate recordDate = Instant.ofEpochSecond(dateTimestamp)
                            .atZone(ZoneId.of("Asia/Shanghai"))
                            .toLocalDate();
                    summary.setRecordDate(recordDate);

                    // 设置星期几
                    summary.setDayOfWeek(recordDate.getDayOfWeek().getDisplayName(
                            java.time.format.TextStyle.FULL, java.util.Locale.CHINA));
                }

                // 打卡时间信息 - 从summary_info中获取
                Long earliestTimestamp = getLongValue(summaryInfo, "earliest_time");
                if (earliestTimestamp != null && earliestTimestamp > 0) {
                    summary.setEarliestCheck(Instant.ofEpochSecond(earliestTimestamp)
                            .atZone(ZoneId.of("Asia/Shanghai"))
                            .toLocalTime());
                }

                Long latestTimestamp = getLongValue(summaryInfo, "lastest_time"); // 注意：企微API中是lastest_time
                if (latestTimestamp != null && latestTimestamp > 0) {
                    summary.setLatestCheck(Instant.ofEpochSecond(latestTimestamp)
                            .atZone(ZoneId.of("Asia/Shanghai"))
                            .toLocalTime());
                }

                // 打卡次数
                summary.setCheckCount(getIntegerValue(summaryInfo, "checkin_count"));

                // 工作时长信息 - 从summary_info中获取
                summary.setStandardHours(getBigDecimalValue(summaryInfo, "standard_work_sec", 3600)); // 秒转小时
                summary.setActualHours(getBigDecimalValue(summaryInfo, "regular_work_sec", 3600)); // 秒转小时

                // 计算按9小时算的工作时长
                BigDecimal actualHours = summary.getActualHours();
                if (actualHours != null) {
                    BigDecimal calculatedHours = actualHours.min(new BigDecimal("9"));
                    summary.setCalculatedHours(calculatedHours);
                }

                // 设置默认职务（如果没有从其他地方获取到）
                if (summary.getPosition() == null || summary.getPosition().isEmpty()) {
                    summary.setPosition("员工");
                }

                summaryList.add(summary);

            } catch (Exception e) {
                log.error("转换企微日报数据失败: {}", dayData, e);
            }
        }

        return summaryList;
    }

    /**
     * 将企业微信打卡详情数据转换为系统详情数据
     */
    private List<AttendanceDetail> convertToDetailList(List<Map<String, Object>> checkinDataList) {
        List<AttendanceDetail> detailList = new ArrayList<>();

        for (Map<String, Object> checkinData : checkinDataList) {
            try {
                AttendanceDetail detail = new AttendanceDetail();

                // 基本信息 - 企微打卡详情API返回的是平铺结构
                detail.setStaffNo(getStringValue(checkinData, "userid"));

                // 注意：企微打卡详情API可能不直接返回name等信息，需要通过其他方式获取
                // 这里先设置为userid，后续可以通过用户信息API补充
                String userName = getStringValue(checkinData, "name");
                if (userName == null || userName.isEmpty()) {
                    userName = getStringValue(checkinData, "userid"); // 如果没有name，使用userid
                }
                detail.setName(userName);

                // 部门信息可能需要通过其他API获取，这里先设置默认值
                detail.setDepartment("未知部门");
                detail.setPosition("员工");

                // 打卡日期和时间 - 企微返回的是时间戳，需要转换
                Long checkinTimestamp = getLongValue(checkinData, "checkin_time");
                if (checkinTimestamp != null) {
                    ZonedDateTime checkinDateTime = Instant.ofEpochSecond(checkinTimestamp)
                            .atZone(ZoneId.of("Asia/Shanghai"));
                    detail.setCheckDate(checkinDateTime.toLocalDate());
                    detail.setActualTime(checkinDateTime.toLocalTime());

                    // 设置星期几
                    detail.setDayOfWeek(checkinDateTime.toLocalDate().getDayOfWeek().getDisplayName(
                            java.time.format.TextStyle.FULL, java.util.Locale.CHINA));
                }

                // 打卡类型 - 企微返回的是中文描述
                String checkinType = getStringValue(checkinData, "checkin_type");
                if (checkinType != null) {
                    if (checkinType.contains("上班")) {
                        detail.setCheckType("上班");
                    } else if (checkinType.contains("下班")) {
                        detail.setCheckType("下班");
                    } else if (checkinType.contains("外出")) {
                        detail.setCheckType("外出");
                    } else {
                        detail.setCheckType(checkinType);
                    }
                } else {
                    detail.setCheckType("未知");
                }

                // 打卡状态 - 根据异常类型判断
                String exceptionType = getStringValue(checkinData, "exception_type");
                if (exceptionType == null || exceptionType.isEmpty()) {
                    detail.setCheckStatus("正常");
                } else {
                    // 企微可能返回多个异常，用分号分隔
                    detail.setCheckStatus(exceptionType);
                }

                // 打卡地点
                detail.setLocation(getStringValue(checkinData, "location_title"));

                // 设备信息
                detail.setDeviceId(getStringValue(checkinData, "deviceid"));

                // 备注信息
                detail.setRemark(getStringValue(checkinData, "notes"));

                // 工作规则 - 从企微返回的groupname获取
                String groupName = getStringValue(checkinData, "groupname");
                detail.setWorkRule(groupName != null ? groupName : "默认规则");

                // 应打卡时间 - 从sch_checkin_time获取
                Long schCheckinTime = getLongValue(checkinData, "sch_checkin_time");
                if (schCheckinTime != null) {
                    LocalTime scheduledTime = Instant.ofEpochSecond(schCheckinTime)
                            .atZone(ZoneId.of("Asia/Shanghai"))
                            .toLocalTime();
                    detail.setScheduledTime(scheduledTime);
                } else {
                    // 默认应打卡时间
                    detail.setScheduledTime(LocalTime.of(9, 0));
                }

                detailList.add(detail);

            } catch (Exception e) {
                log.error("转换企微打卡详情数据失败: {}", checkinData, e);
            }
        }

        return detailList;
    }

    /**
     * 从Map中获取字符串值
     */
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 从Map中获取Long值
     */
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 从Map中获取Integer值
     */
    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 从Map中获取BigDecimal值，并进行单位转换
     */
    private BigDecimal getBigDecimalValue(Map<String, Object> map, String key, int divisor) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        try {
            BigDecimal bigDecimalValue;
            if (value instanceof Number) {
                bigDecimalValue = new BigDecimal(value.toString());
            } else {
                bigDecimalValue = new BigDecimal(value.toString());
            }

            if (divisor > 1) {
                return bigDecimalValue.divide(new BigDecimal(divisor), 2, BigDecimal.ROUND_HALF_UP);
            }
            return bigDecimalValue;
        } catch (NumberFormatException e) {
            return null;
        }
    }
}