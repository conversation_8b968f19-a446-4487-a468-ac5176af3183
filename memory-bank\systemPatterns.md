# TimeTrex System Patterns

## Architectural Style
- Modular monolith with clear component boundaries
- Layered architecture (presentation, business, data)
- Shared common utilities library

## Key Design Patterns
1. **Memory Bank Pattern**
   - Centralized documentation system
   - Implemented in MemoryBank.java
   - Used for maintaining project knowledge

2. **MV<PERSON> Pattern**
   - Used in web controllers (StaffInfoController)
   - Separation of concerns in admin interface

3. **Factory Pattern**
   - Used in framework components
   - Object creation abstraction

4. **Observer Pattern**
   - Used in event handling system
   - Notification mechanisms

## Component Relationships
- timetrex-admin depends on timetrex-common and timetrex-framework
- timetrex-system provides core services to other modules
- timetrex-quartz handles scheduled jobs independently
- All modules share common utilities from timetrex-common

## Critical Paths
1. Staff information management flow
2. Time tracking processing pipeline
3. Payroll calculation engine
4. Scheduled job execution
