# 工作量登记功能问题排查指南

## 问题描述
录入姓名获取到工号后出现异常：
```
10:06:33.992 [http-nio-80-exec-36] ERROR c.t.f.w.e.GlobalExceptionHandler - [handleRuntimeException,70] - 请求地址'/system/vendor/getById',发生未知异常.
```

## 问题分析

### 可能原因：
1. **厂商ID为空或无效**：员工信息中的厂商ID字段为空或指向不存在的厂商
2. **数据库表结构问题**：厂商表或员工表缺少必要字段
3. **权限问题**：访问厂商信息接口时权限不足
4. **数据类型转换问题**：厂商ID的数据类型转换异常

## 已实施的修复措施

### 1. 后端API异常处理优化

#### VendorController.getById()方法：
```java
@GetMapping("/getById")
@ResponseBody
public AjaxResult getById(Long id) {
    try {
        if (id == null) {
            return AjaxResult.error("厂商ID不能为空");
        }
        Vendor vendor = vendorService.selectVendorById(id);
        if (vendor == null) {
            return AjaxResult.error("未找到对应的厂商信息");
        }
        return AjaxResult.success(vendor);
    } catch (Exception e) {
        return AjaxResult.error("获取厂商信息失败：" + e.getMessage());
    }
}
```

#### VendorController.getAvgWorkDays()方法：
```java
@GetMapping("/getAvgWorkDays")
@ResponseBody
public AjaxResult getAvgWorkDays(Long vendorId) {
    try {
        if (vendorId == null) {
            return AjaxResult.success(22.0); // 默认22天
        }
        Vendor vendor = vendorService.selectVendorById(vendorId);
        if (vendor != null && vendor.getAvgWorkDays() != null) {
            return AjaxResult.success(vendor.getAvgWorkDays());
        }
        return AjaxResult.success(22.0); // 默认22天
    } catch (Exception e) {
        return AjaxResult.success(22.0); // 异常时返回默认值
    }
}
```

### 2. 前端JavaScript优化

#### 增加空值检查：
```javascript
function getVendorInfo(vendorId) {
    if (!vendorId) {
        console.log("厂商ID为空，跳过获取厂商信息");
        return;
    }
    // ... 其他逻辑
}
```

#### 增加详细的错误处理和调试信息：
```javascript
success: function(result) {
    console.log("获取厂商信息结果：", result);
    if (result.code == 0 && result.data) {
        $("#vendorName").val(result.data.name);
        calculatePersonMonths();
    } else {
        console.log("获取厂商信息失败：", result.msg);
        $("#vendorName").val("");
    }
},
error: function(xhr, status, error) {
    console.error("获取厂商信息异常：", error);
    $.modal.alertError("获取厂商信息失败");
}
```

## 排查步骤

### 1. 检查数据库表结构

#### 确认员工表(staff_info)包含必要字段：
```sql
DESCRIBE staff_info;
-- 确认包含以下字段：
-- vendor_id (厂商ID)
-- staff_no (员工工号)
-- name (员工姓名)
-- unit_price (人员单价)
-- status (状态)
```

#### 确认厂商表(vendor)包含必要字段：
```sql
DESCRIBE vendor;
-- 确认包含以下字段：
-- id (厂商ID)
-- name (厂商名称)
-- avg_work_days (平均工作日)
```

### 2. 检查数据完整性

#### 查询员工数据：
```sql
SELECT id, staff_no, name, vendor_id, unit_price, status 
FROM staff_info 
WHERE name = '测试员工姓名';
```

#### 查询厂商数据：
```sql
SELECT id, name, avg_work_days 
FROM vendor 
WHERE id = (SELECT vendor_id FROM staff_info WHERE name = '测试员工姓名');
```

### 3. 测试API接口

#### 测试员工信息接口：
```bash
curl -X GET "http://localhost/system/staff_info/getByStaffName?staffName=测试员工姓名"
```

#### 测试厂商信息接口：
```bash
curl -X GET "http://localhost/system/vendor/getById?id=1"
```

### 4. 检查浏览器控制台

打开浏览器开发者工具，查看：
1. **Console标签**：查看JavaScript错误和调试信息
2. **Network标签**：查看API请求和响应
3. **Response内容**：确认API返回的数据格式

## 常见问题及解决方案

### 问题1：厂商ID为空
**现象**：员工信息中vendor_id字段为NULL
**解决**：
```sql
-- 更新员工的厂商关联
UPDATE staff_info SET vendor_id = 1 WHERE vendor_id IS NULL;
```

### 问题2：厂商不存在
**现象**：员工关联的厂商ID在厂商表中不存在
**解决**：
```sql
-- 查找孤立的厂商关联
SELECT s.*, v.id as vendor_exists 
FROM staff_info s 
LEFT JOIN vendor v ON s.vendor_id = v.id 
WHERE s.vendor_id IS NOT NULL AND v.id IS NULL;

-- 修复数据或创建缺失的厂商
```

### 问题3：权限不足
**现象**：访问API时返回权限错误
**解决**：确认用户具有相应权限，或临时移除接口的权限注解

### 问题4：数据类型问题
**现象**：厂商ID数据类型不匹配
**解决**：确认数据库字段类型与Java实体类型一致

## 调试建议

### 1. 启用详细日志
在application.yml中添加：
```yaml
logging:
  level:
    com.timetrex: DEBUG
    org.springframework.web: DEBUG
```

### 2. 使用浏览器调试
1. 打开开发者工具
2. 在Console中查看调试信息
3. 在Network中查看API请求详情

### 3. 后端断点调试
在以下方法中设置断点：
- `VendorController.getById()`
- `StaffInfoController.getByStaffName()`
- `VendorServiceImpl.selectVendorById()`

## 预防措施

### 1. 数据完整性约束
```sql
-- 添加外键约束
ALTER TABLE staff_info 
ADD CONSTRAINT fk_staff_vendor 
FOREIGN KEY (vendor_id) REFERENCES vendor(id);
```

### 2. 默认值设置
```sql
-- 为厂商表设置默认平均工作日
ALTER TABLE vendor 
ALTER COLUMN avg_work_days SET DEFAULT 22.0;
```

### 3. 数据验证
在保存员工信息时验证厂商关联的有效性

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 完整的错误日志
2. 浏览器控制台截图
3. 相关数据库记录
4. API请求和响应详情

这样可以更快速地定位和解决问题。
