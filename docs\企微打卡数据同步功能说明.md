# 企业微信打卡数据同步功能说明

## 功能概述

本功能实现了从企业微信获取打卡数据并同步到系统中，与Excel导出功能共用同一套数据表，确保数据的一致性。

## 主要特性

1. **数据同步**：支持按月份同步企业微信打卡数据
2. **时间转换**：自动将企微API返回的时间戳转换为北京时间
3. **数据兼容**：同步的数据与Excel导出功能完全兼容
4. **错误处理**：完善的错误处理和日志记录

## 配置说明

### 1. 企业微信配置

在 `application.yml` 中配置企业微信相关参数：

```yaml
# 企业微信配置
wechat:
  # 企业ID
  corpid: your_corp_id_here
  # 打卡应用的Secret
  checkin:
    secret: your_checkin_secret_here
```

### 2. 数据库菜单配置

执行以下SQL添加菜单权限：

```sql
-- 执行 sql/wechat_attendance_sync_menu.sql 文件
```

## 使用方法

1. **访问同步页面**：系统管理 -> 考勤管理 -> 企微打卡同步
2. **选择年月**：选择要同步的年月份
3. **执行同步**：点击"同步"按钮开始数据同步
4. **查看结果**：同步完成后会显示同步的数据条数

## 数据结构

### 同步的数据类型

1. **考勤概况数据** (`attendance_summary`)
   - 员工基本信息（工号、姓名、部门、职务）
   - 日期信息（记录日期、星期几）
   - 打卡统计（最早打卡时间、最晚打卡时间、打卡次数）
   - 工作时长（标准工作时长、实际工作时长、按9小时计算的时长）

2. **打卡详情数据** (`attendance_detail`)
   - 员工基本信息（工号、姓名、部门、职务）
   - 打卡信息（打卡日期、打卡时间、打卡类型）
   - 状态信息（打卡状态、打卡地点、设备ID）
   - 备注信息（备注内容、假勤申请）

### 时间戳转换

企业微信API返回的时间戳会自动转换为：
- 日期：转换为 `LocalDate` 格式
- 时间：转换为 `LocalTime` 格式
- 时区：统一使用北京时间（Asia/Shanghai）

## API接口

### 企业微信API调用

1. **获取访问令牌**
   - URL: `https://qyapi.weixin.qq.com/cgi-bin/gettoken`
   - 参数: `corpid`, `corpsecret`

2. **获取打卡日报数据**
   - URL: `https://qyapi.weixin.qq.com/cgi-bin/checkin/getcheckin_daydata`
   - 参数: `starttime`, `endtime`, `useridlist`

3. **获取打卡详情数据**
   - URL: `https://qyapi.weixin.qq.com/cgi-bin/checkin/getcheckindata`
   - 参数: `starttime`, `endtime`, `useridlist`

## 修复的问题

### 1. 页面加载异常修复 ✅
- **问题**：`laydate is not defined` 错误
- **解决方案**：正确使用layui模块加载laydate
- **修复代码**：
```javascript
// 使用layui的laydate模块
layui.use('laydate', function(){
    var laydate = layui.laydate;

    // 初始化年月选择器
    laydate.render({
        elem: '#yearMonth',
        type: 'month',
        format: 'yyyy-MM',
        value: new Date(),
        max: 0, // 不能选择未来的月份
        theme: 'molv'
    });
});
```

### 2. 时间范围优化 ✅
- **问题**：需要确保时间范围是每月1号到月底
- **解决方案**：
  - 起始时间：每月1号 00:00:00
  - 结束时间：每月最后一天 23:59:59
- **修复代码**：
```java
// 解析年月，确保时间范围是每月1号到月底
YearMonth ym = YearMonth.parse(yearMonth);
LocalDate startDate = ym.atDay(1); // 每月1号
LocalDate endDate = ym.atEndOfMonth(); // 每月最后一天

// 时间戳转换
params.put("starttime", startDate.atStartOfDay().toEpochSecond(ZoneOffset.of("+8")));
params.put("endtime", endDate.atTime(23, 59, 59).toEpochSecond(ZoneOffset.of("+8")));
```

## 注意事项

1. **权限配置**：确保企业微信应用有打卡数据的读取权限
2. **网络连接**：确保服务器能够访问企业微信API
3. **数据量限制**：企业微信API可能有调用频率限制
4. **时区处理**：所有时间都会转换为北京时间（Asia/Shanghai）
5. **数据覆盖**：同步时会使用批量插入，请注意数据重复问题
6. **时间范围**：确保选择的年月范围覆盖完整的月份（1号到月底）

## 错误处理

系统会记录详细的错误日志，包括：
- API调用失败
- JSON解析错误
- 数据转换异常
- 数据库操作失败

查看日志文件获取详细的错误信息。

## 技术实现

- **JSON解析**：使用Jackson库解析企微API响应
- **HTTP请求**：使用项目内置的HttpUtils工具类
- **数据转换**：实现了完整的企微数据到系统数据的映射
- **批量操作**：使用MyBatis批量插入提高性能
- **事务管理**：使用Spring事务确保数据一致性
