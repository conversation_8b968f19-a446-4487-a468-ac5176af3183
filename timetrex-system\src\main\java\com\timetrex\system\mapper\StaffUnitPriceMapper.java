package com.timetrex.system.mapper;

import java.util.List;
import java.util.Date;
import com.timetrex.system.domain.StaffUnitPrice;

/**
 * 人员单价配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-20
 */
public interface StaffUnitPriceMapper 
{
    /**
     * 查询人员单价配置
     * 
     * @param id 人员单价配置主键
     * @return 人员单价配置
     */
    public StaffUnitPrice selectStaffUnitPriceById(Long id);

    /**
     * 查询人员单价配置列表
     * 
     * @param staffUnitPrice 人员单价配置
     * @return 人员单价配置集合
     */
    public List<StaffUnitPrice> selectStaffUnitPriceList(StaffUnitPrice staffUnitPrice);

    /**
     * 新增人员单价配置
     * 
     * @param staffUnitPrice 人员单价配置
     * @return 结果
     */
    public int insertStaffUnitPrice(StaffUnitPrice staffUnitPrice);

    /**
     * 修改人员单价配置
     * 
     * @param staffUnitPrice 人员单价配置
     * @return 结果
     */
    public int updateStaffUnitPrice(StaffUnitPrice staffUnitPrice);

    /**
     * 删除人员单价配置
     * 
     * @param id 人员单价配置主键
     * @return 结果
     */
    public int deleteStaffUnitPriceById(Long id);

    /**
     * 批量删除人员单价配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStaffUnitPriceByIds(Long[] ids);

    /**
     * 根据员工工号和日期查询有效的单价配置
     * 
     * @param staffNo 员工工号
     * @param targetDate 目标日期
     * @return 单价配置
     */
    public StaffUnitPrice selectEffectivePriceByStaffAndDate(String staffNo, Date targetDate);

    /**
     * 根据厂商ID查询所有有效的单价配置
     * 
     * @param vendorId 厂商ID
     * @return 单价配置列表
     */
    public List<StaffUnitPrice> selectEffectivePriceByVendor(Long vendorId);

    /**
     * 检查员工在指定日期是否已有单价配置
     * 
     * @param vendorId 厂商ID
     * @param staffNo 员工工号
     * @param effectiveDate 生效日期
     * @param excludeId 排除的ID（用于修改时排除自己）
     * @return 数量
     */
    public int checkDuplicatePrice(Long vendorId, String staffNo, Date effectiveDate, Long excludeId);
}
