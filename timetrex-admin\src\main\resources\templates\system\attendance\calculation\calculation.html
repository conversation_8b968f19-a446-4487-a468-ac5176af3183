<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('考勤核算')" />
    <th:block th:include="include :: bootstrap-select-css" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>月份：</p>
                                <input type="text" class="form-control" id="month" name="month" placeholder="请选择月份" readonly/>
                            </li>
                            <li>
                                <p>厂商：</p>
                                <select name="vendorId" id="vendorId" class="form-control">
                                    <option value="">所有厂商</option>
                                    <option th:each="vendor : ${vendorList}" th:value="${vendor.id}" th:text="${vendor.name}"></option>
                                </select>
                            </li>
                            <li>
                                <p>人员：</p>
                                <input type="text" name="staffName"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="calculate()" shiro:hasPermission="system:attendance:calculation:calculate">
                    <i class="fa fa-calculator"></i> 执行核算
                </a>
                <a class="btn btn-primary" onclick="$.table.exportExcel()" shiro:hasPermission="system:attendance:calculation:export">
                    <i class="fa fa-download"></i> 导出
                </a>
                <a class="btn btn-info" onclick="sendResult()" shiro:hasPermission="system:attendance:calculation:send">
                    <i class="fa fa-envelope"></i> 发送结果
                </a>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <div class="modal fade" id="calculateModal" tabindex="-1" role="dialog" aria-labelledby="calculateModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title" id="calculateModalLabel">执行考勤核算</h4>
                </div>
                <div class="modal-body">
                    <form id="calculateForm" class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">月份：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="calcMonth" name="calcMonth" placeholder="请选择月份" readonly/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">厂商：</label>
                            <div class="col-sm-8">
                                <select name="calcVendorIds" id="calcVendorIds" class="form-control selectpicker" multiple data-live-search="true" data-actions-box="true" title="请选择厂商">
                                    <option th:each="vendor : ${vendorList}" th:value="${vendor.id}" th:text="${vendor.name}"></option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="submitCalculateForm()">执行核算</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="sendModal" tabindex="-1" role="dialog" aria-labelledby="sendModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title" id="sendModalLabel">发送核算结果</h4>
                </div>
                <div class="modal-body">
                    <form id="sendForm" class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">月份：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" id="sendMonth" name="sendMonth" placeholder="请选择月份" readonly/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">厂商：</label>
                            <div class="col-sm-8">
                                <select name="sendVendorId" id="sendVendorId" class="form-control selectpicker" data-live-search="true" title="请选择厂商">
                                    <option th:each="vendor : ${vendorList}" th:value="${vendor.id}" th:text="${vendor.name}"></option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label">接收邮箱：</label>
                            <div class="col-sm-8">
                                <input type="email" class="form-control" id="email" name="email" placeholder="请输入接收邮箱"/>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="submitSendForm()">发送</button>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-select-js" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/attendance/calculation";

        $(function() {
            var options = {
                url: prefix + "/list",
                exportUrl: prefix + "/export",
                modalName: "核算结果",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'staffName',
                    title: '人员姓名'
                },
                {
                    field: 'vendorName',
                    title: '厂商名称'
                },
                {
                    field: 'month',
                    title: '核算月份'
                },
                {
                    field: 'standardHours',
                    title: '标准工作时长'
                },
                {
                    field: 'calculatedHours',
                    title: '实际打卡按9小时算'
                },
                {
                    field: 'workDays',
                    title: '人月工时'
                },
                {
                    field: 'amount',
                    title: '结算金额'
                }]
            };
            $.table.init(options);

            // 初始化月份选择器
            $('#month, #calcMonth, #sendMonth').datetimepicker({
                format: 'yyyy-mm',
                minView: 'month',
                startView: 'year',
                autoclose: true,
                language: 'zh-CN'
            });

            // 初始化多选下拉框
            $('.selectpicker').selectpicker({
                noneSelectedText: '请选择',
                selectAllText: '全选',
                deselectAllText: '取消全选',
                multipleSeparator: ', '
            });
        });

        // 打开执行核算模态框
        function calculate() {
            $("#calculateModal").modal("show");
        }

        // 提交执行核算表单
        function submitCalculateForm() {
            var month = $("#calcMonth").val();
            var vendorIds = $("#calcVendorIds").val();

            if (!month) {
                $.modal.alertWarning("请选择月份");
                return;
            }

            if (!vendorIds || vendorIds.length === 0) {
                $.modal.alertWarning("请选择至少一个厂商");
                return;
            }

            var vendorCount = vendorIds.length;
            var loadingMsg = vendorCount > 1
                ? "正在执行 " + vendorCount + " 个厂商的核算，请稍候..."
                : "正在执行核算，请稍候...";
            $.modal.loading(loadingMsg);
            $.ajax({
                url: prefix + "/calculate",
                type: "post",
                data: {
                    "month": month,
                    "vendorIds": vendorIds.join(',')
                },
                success: function(result) {
                    $.modal.closeLoading();
                    if (result.code == web_status.SUCCESS) {
                        $.modal.closeAll();
                        $.modal.alertSuccess(result.msg);
                        $.table.refresh();
                    } else {
                        $.modal.alertError(result.msg);
                    }
                },
                error: function() {
                    $.modal.closeLoading();
                    $.modal.alertError("系统错误，请稍后重试");
                }
            });
        }

        // 打开发送结果模态框
        function sendResult() {
            $("#sendModal").modal("show");
        }

        // 提交发送结果表单
        function submitSendForm() {
            var month = $("#sendMonth").val();
            var vendorId = $("#sendVendorId").val();
            var email = $("#email").val();

            if (!month) {
                $.modal.alertWarning("请选择月份");
                return;
            }

            if (!vendorId) {
                $.modal.alertWarning("请选择厂商");
                return;
            }

            if (!email) {
                $.modal.alertWarning("请输入接收邮箱");
                return;
            }

            $.ajax({
                url: prefix + "/sendResult",
                type: "post",
                data: {
                    "month": month,
                    "vendorId": vendorId,
                    "email": email
                },
                success: function(result) {
                    if (result.code == web_status.SUCCESS) {
                        $.modal.closeAll();
                        $.modal.alertSuccess(result.msg);
                    } else {
                        $.modal.alertError(result.msg);
                    }
                }
            });
        }
    </script>
</body>
</html>
