<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('季度归总报表')" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>年份：</p>
                                <select name="year" id="year" class="form-control">
                                    <option th:each="year : ${yearList}" th:value="${year}" th:text="${year}" th:selected="${year == currentYear}"></option>
                                </select>
                            </li>
                            <li>
                                <p>季度：</p>
                                <select name="quarter" id="quarter" class="form-control">
                                    <option value="1">第一季度</option>
                                    <option value="2">第二季度</option>
                                    <option value="3">第三季度</option>
                                    <option value="4">第四季度</option>
                                </select>
                            </li>
                            <li>
                                <p>厂商：</p>
                                <select name="vendorIds" id="vendorIds" class="form-control selectpicker" multiple data-live-search="true" data-actions-box="true" title="请选择厂商">
                                    <option th:each="vendor : ${vendorList}" th:value="${vendor.id}" th:text="${vendor.name}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="searchReport()"><i class="fa fa-search"></i>&nbsp;查询</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="resetForm()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-primary" onclick="exportReport()" shiro:hasPermission="system:quarterly:report:export">
                    <i class="fa fa-download"></i> 导出
                </a>
                <a class="btn btn-info" onclick="toggleMonthlyColumns()">
                    <i class="fa fa-columns"></i> <span id="toggleText">显示月度数据</span>
                </a>
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-select-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/quarterly/report";

        $(function() {
            var options = {
                // 初始不加载数据
                url: null,
                exportUrl: prefix + "/export",
                modalName: "季度归总报表",
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                columns: [{
                    checkbox: true
                },
                {
                    field: 'staffName',
                    title: '人员姓名'
                },
                {
                    field: 'vendorName',
                    title: '厂商名称'
                },
                {
                    field: 'year',
                    title: '年份'
                },
                {
                    field: 'quarter',
                    title: '季度'
                },
                {
                    field: 'standardHours1',
                    title: '第一个月标准工作时长',
                    visible: false,
                    formatter: function(value, row, index) {
                        if (value !== null && value !== undefined && typeof value === 'number') {
                            return value.toFixed(2);
                        } else if (value !== null && value !== undefined && typeof value === 'string' && !isNaN(parseFloat(value))) {
                            return parseFloat(value).toFixed(2);
                        } else {
                            return "0.00";
                        }
                    }
                },
                {
                    field: 'calculatedHours1',
                    title: '第一个月实际打卡按9小时算',
                    visible: false,
                    formatter: function(value, row, index) {
                        if (value !== null && value !== undefined && typeof value === 'number') {
                            return value.toFixed(2);
                        } else if (value !== null && value !== undefined && typeof value === 'string' && !isNaN(parseFloat(value))) {
                            return parseFloat(value).toFixed(2);
                        } else {
                            return "0.00";
                        }
                    }
                },
                {
                    field: 'workDays1',
                    title: '第一个月人月工时',
                    visible: false,
                    formatter: function(value, row, index) {
                        if (value !== null && value !== undefined && typeof value === 'number') {
                            return value.toFixed(2);
                        } else if (value !== null && value !== undefined && typeof value === 'string' && !isNaN(parseFloat(value))) {
                            return parseFloat(value).toFixed(2);
                        } else {
                            return "0.00";
                        }
                    }
                },
                {
                    field: 'amount1',
                    title: '第一个月结算金额',
                    visible: false,
                    formatter: function(value, row, index) {
                        if (value !== null && value !== undefined && typeof value === 'number') {
                            return value.toFixed(2);
                        } else if (value !== null && value !== undefined && typeof value === 'string' && !isNaN(parseFloat(value))) {
                            return parseFloat(value).toFixed(2);
                        } else {
                            return "0.00";
                        }
                    }
                },
                {
                    field: 'standardHours2',
                    title: '第二个月标准工作时长',
                    visible: false,
                    formatter: function(value, row, index) {
                        if (value !== null && value !== undefined && typeof value === 'number') {
                            return value.toFixed(2);
                        } else if (value !== null && value !== undefined && typeof value === 'string' && !isNaN(parseFloat(value))) {
                            return parseFloat(value).toFixed(2);
                        } else {
                            return "0.00";
                        }
                    }
                },
                {
                    field: 'calculatedHours2',
                    title: '第二个月实际打卡按9小时算',
                    visible: false,
                    formatter: function(value, row, index) {
                        if (value !== null && value !== undefined && typeof value === 'number') {
                            return value.toFixed(2);
                        } else if (value !== null && value !== undefined && typeof value === 'string' && !isNaN(parseFloat(value))) {
                            return parseFloat(value).toFixed(2);
                        } else {
                            return "0.00";
                        }
                    }
                },
                {
                    field: 'workDays2',
                    title: '第二个月人月工时',
                    visible: false,
                    formatter: function(value, row, index) {
                        if (value !== null && value !== undefined && typeof value === 'number') {
                            return value.toFixed(2);
                        } else if (value !== null && value !== undefined && typeof value === 'string' && !isNaN(parseFloat(value))) {
                            return parseFloat(value).toFixed(2);
                        } else {
                            return "0.00";
                        }
                    }
                },
                {
                    field: 'amount2',
                    title: '第二个月结算金额',
                    visible: false,
                    formatter: function(value, row, index) {
                        if (value !== null && value !== undefined && typeof value === 'number') {
                            return value.toFixed(2);
                        } else if (value !== null && value !== undefined && typeof value === 'string' && !isNaN(parseFloat(value))) {
                            return parseFloat(value).toFixed(2);
                        } else {
                            return "0.00";
                        }
                    }
                },
                {
                    field: 'standardHours3',
                    title: '第三个月标准工作时长',
                    visible: false,
                    formatter: function(value, row, index) {
                        if (value !== null && value !== undefined && typeof value === 'number') {
                            return value.toFixed(2);
                        } else if (value !== null && value !== undefined && typeof value === 'string' && !isNaN(parseFloat(value))) {
                            return parseFloat(value).toFixed(2);
                        } else {
                            return "0.00";
                        }
                    }
                },
                {
                    field: 'calculatedHours3',
                    title: '第三个月实际打卡按9小时算',
                    visible: false,
                    formatter: function(value, row, index) {
                        if (value !== null && value !== undefined && typeof value === 'number') {
                            return value.toFixed(2);
                        } else if (value !== null && value !== undefined && typeof value === 'string' && !isNaN(parseFloat(value))) {
                            return parseFloat(value).toFixed(2);
                        } else {
                            return "0.00";
                        }
                    }
                },
                {
                    field: 'workDays3',
                    title: '第三个月人月工时',
                    visible: false,
                    formatter: function(value, row, index) {
                        if (value !== null && value !== undefined && typeof value === 'number') {
                            return value.toFixed(2);
                        } else if (value !== null && value !== undefined && typeof value === 'string' && !isNaN(parseFloat(value))) {
                            return parseFloat(value).toFixed(2);
                        } else {
                            return "0.00";
                        }
                    }
                },
                {
                    field: 'amount3',
                    title: '第三个月结算金额',
                    visible: false,
                    formatter: function(value, row, index) {
                        if (value !== null && value !== undefined && typeof value === 'number') {
                            return value.toFixed(2);
                        } else if (value !== null && value !== undefined && typeof value === 'string' && !isNaN(parseFloat(value))) {
                            return parseFloat(value).toFixed(2);
                        } else {
                            return "0.00";
                        }
                    }
                },
                {
                    field: 'totalStandardHours',
                    title: '季度标准工作时长总和',
                    formatter: function(value, row, index) {
                        if (value !== null && value !== undefined && typeof value === 'number') {
                            return value.toFixed(2);
                        } else if (value !== null && value !== undefined && typeof value === 'string' && !isNaN(parseFloat(value))) {
                            return parseFloat(value).toFixed(2);
                        } else {
                            return "0.00";
                        }
                    }
                },
                {
                    field: 'totalCalculatedHours',
                    title: '季度实际打卡按9小时算总和',
                    formatter: function(value, row, index) {
                        if (value !== null && value !== undefined && typeof value === 'number') {
                            return value.toFixed(2);
                        } else if (value !== null && value !== undefined && typeof value === 'string' && !isNaN(parseFloat(value))) {
                            return parseFloat(value).toFixed(2);
                        } else {
                            return "0.00";
                        }
                    }
                },
                {
                    field: 'totalWorkDays',
                    title: '季度人月工时总和',
                    formatter: function(value, row, index) {
                        if (value !== null && value !== undefined && typeof value === 'number') {
                            return value.toFixed(2);
                        } else if (value !== null && value !== undefined && typeof value === 'string' && !isNaN(parseFloat(value))) {
                            return parseFloat(value).toFixed(2);
                        } else {
                            return "0.00";
                        }
                    }
                },
                {
                    field: 'totalAmount',
                    title: '季度结算金额总和',
                    formatter: function(value, row, index) {
                        if (value !== null && value !== undefined && typeof value === 'number') {
                            return value.toFixed(2);
                        } else if (value !== null && value !== undefined && typeof value === 'string' && !isNaN(parseFloat(value))) {
                            return parseFloat(value).toFixed(2);
                        } else {
                            return "0.00";
                        }
                    }
                },
                {
                    title: '详细信息',
                    formatter: function(value, row, index) {
                        return '<a class="btn btn-info btn-xs" onclick="showDetail(\'' + row.staffName + '\')"><i class="fa fa-search"></i> 查看详情</a>';
                    }
                }]
            };
            $.table.init(options);
            
            // 初始化多选下拉框
            $('.selectpicker').selectpicker({
                noneSelectedText: '请选择',
                selectAllText: '全选',
                deselectAllText: '取消全选',
                multipleSeparator: ', '
            });
        });
        
        // 查询报表
        function searchReport() {
            var year = $("#year").val();
            var quarter = $("#quarter").val();
            var vendorIds = $("#vendorIds").val();
            
            if (!year) {
                $.modal.alertWarning("请选择年份");
                return;
            }
            
            if (!quarter) {
                $.modal.alertWarning("请选择季度");
                return;
            }
            
            if (!vendorIds || vendorIds.length === 0) {
                $.modal.alertWarning("请选择至少一个厂商");
                return;
            }
            
            var params = {
                "year": year,
                "quarter": quarter,
                "vendorIds": vendorIds.join(',')
            };
            
            $("#bootstrap-table").bootstrapTable('refresh', {
                url: prefix + '/list',
                query: params
            });
        }
        
        // 导出报表
        function exportReport() {
            var year = $("#year").val();
            var quarter = $("#quarter").val();
            var vendorIds = $("#vendorIds").val();
            
            if (!year) {
                $.modal.alertWarning("请选择年份");
                return;
            }
            
            if (!quarter) {
                $.modal.alertWarning("请选择季度");
                return;
            }
            
            if (!vendorIds || vendorIds.length === 0) {
                $.modal.alertWarning("请选择至少一个厂商");
                return;
            }
            
            $.modal.loading("正在导出数据，请稍候...");
            $.post(prefix + "/export", {
                "year": year,
                "quarter": quarter,
                "vendorIds": vendorIds.join(',')
            }, function(result) {
                $.modal.closeLoading();
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else {
                    $.modal.alertError(result.msg);
                }
            });
        }
        
        // 重置表单
        function resetForm() {
            $("#formId")[0].reset();
            $('.selectpicker').selectpicker('refresh');
        }
        
        // 切换显示/隐藏月度列
        function toggleMonthlyColumns() {
            var $table = $("#bootstrap-table");
            var monthlyColumnsVisible = $("#toggleText").text() === "隐藏月度数据";
            
            // 月度列的索引（从0开始）
            var monthlyColumnIndices = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16];
            
            // 切换月度列的可见性
            for (var i = 0; i < monthlyColumnIndices.length; i++) {
                $table.bootstrapTable('hideColumn', monthlyColumnIndices[i]);
                if (!monthlyColumnsVisible) {
                    $table.bootstrapTable('showColumn', monthlyColumnIndices[i]);
                }
            }
            
            // 更新按钮文本
            $("#toggleText").text(monthlyColumnsVisible ? "显示月度数据" : "隐藏月度数据");
        }
        
        // 显示详细信息
        function showDetail(staffName) {
            var rows = $("#bootstrap-table").bootstrapTable('getData');
            var row = null;
            
            // 查找对应的行数据
            for (var i = 0; i < rows.length; i++) {
                if (rows[i].staffName === staffName) {
                    row = rows[i];
                    break;
                }
            }
            
            if (row) {
                // 构建详细信息HTML
                var detailHtml = '<div class="row">' +
                    '<div class="col-sm-12">' +
                    '<table class="table table-bordered">' +
                    '<thead>' +
                    '<tr>' +
                    '<th>月份</th>' +
                    '<th>标准工作时长</th>' +
                    '<th>实际打卡按9小时算</th>' +
                    '<th>人月工时</th>' +
                    '<th>结算金额</th>' +
                    '</tr>' +
                    '</thead>' +
                    '<tbody>' +
                    '<tr>' +
                    '<td>第一个月</td>' +
                    '<td>' + formatNumber(row.standardHours1) + '</td>' +
                    '<td>' + formatNumber(row.calculatedHours1) + '</td>' +
                    '<td>' + formatNumber(row.workDays1) + '</td>' +
                    '<td>' + formatNumber(row.amount1) + '</td>' +
                    '</tr>' +
                    '<tr>' +
                    '<td>第二个月</td>' +
                    '<td>' + formatNumber(row.standardHours2) + '</td>' +
                    '<td>' + formatNumber(row.calculatedHours2) + '</td>' +
                    '<td>' + formatNumber(row.workDays2) + '</td>' +
                    '<td>' + formatNumber(row.amount2) + '</td>' +
                    '</tr>' +
                    '<tr>' +
                    '<td>第三个月</td>' +
                    '<td>' + formatNumber(row.standardHours3) + '</td>' +
                    '<td>' + formatNumber(row.calculatedHours3) + '</td>' +
                    '<td>' + formatNumber(row.workDays3) + '</td>' +
                    '<td>' + formatNumber(row.amount3) + '</td>' +
                    '</tr>' +
                    '<tr class="success">' +
                    '<td><strong>季度总计</strong></td>' +
                    '<td><strong>' + formatNumber(row.totalStandardHours) + '</strong></td>' +
                    '<td><strong>' + formatNumber(row.totalCalculatedHours) + '</strong></td>' +
                    '<td><strong>' + formatNumber(row.totalWorkDays) + '</strong></td>' +
                    '<td><strong>' + formatNumber(row.totalAmount) + '</strong></td>' +
                    '</tr>' +
                    '</tbody>' +
                    '</table>' +
                    '</div>' +
                    '</div>';
                
                // 显示详细信息
                $.modal.open(row.staffName + " - " + row.year + "年第" + row.quarter + "季度详细信息", detailHtml, '800', '500');
            }
        }
        
        // 格式化数字
        function formatNumber(value) {
            if (value !== null && value !== undefined && typeof value === 'number') {
                return value.toFixed(2);
            } else if (value !== null && value !== undefined && typeof value === 'string' && !isNaN(parseFloat(value))) {
                return parseFloat(value).toFixed(2);
            } else {
                return "0.00";
            }
        }
    </script>
</body>
</html>
