package com.timetrex.system.mapper;

import java.util.List;
import com.timetrex.system.domain.AttendanceDetail;

/**
 * 打卡详情Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-09
 */
public interface AttendanceDetailMapper 
{
    /**
     * 查询打卡详情
     * 
     * @param id 打卡详情主键
     * @return 打卡详情
     */
    public AttendanceDetail selectAttendanceDetailById(Long id);

    /**
     * 查询打卡详情列表
     * 
     * @param attendanceDetail 打卡详情
     * @return 打卡详情集合
     */
    public List<AttendanceDetail> selectAttendanceDetailList(AttendanceDetail attendanceDetail);

    /**
     * 新增打卡详情
     * 
     * @param attendanceDetail 打卡详情
     * @return 结果
     */
    public int insertAttendanceDetail(AttendanceDetail attendanceDetail);

    /**
     * 批量新增打卡详情
     * 
     * @param attendanceDetailList 打卡详情列表
     * @return 结果
     */
    public int batchInsertAttendanceDetail(List<AttendanceDetail> attendanceDetailList);

    /**
     * 修改打卡详情
     * 
     * @param attendanceDetail 打卡详情
     * @return 结果
     */
    public int updateAttendanceDetail(AttendanceDetail attendanceDetail);

    /**
     * 删除打卡详情
     * 
     * @param id 打卡详情主键
     * @return 结果
     */
    public int deleteAttendanceDetailById(Long id);

    /**
     * 批量删除打卡详情
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAttendanceDetailByIds(String[] ids);
    
    /**
     * 检查设备ID是否被多人使用
     * 
     * @param deviceId 设备ID
     * @return 使用该设备的人员数量
     */
    public int checkDeviceUsedByMultipleStaff(String deviceId);
}
