<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('人员工作量登记')" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>厂商：</p>
                                <select name="vendorId" id="vendorId" class="form-control selectpicker" 
                                        data-live-search="true" title="请选择厂商">
                                    <option value="">所有厂商</option>
                                    <option th:each="vendor : ${vendorList}" th:value="${vendor.id}" th:text="${vendor.name}"></option>
                                </select>
                            </li>
                            <li>
                                <p>年份：</p>
                                <select name="recordYear" id="recordYear" class="form-control">
                                    <option value="">请选择年份</option>
                                    <option th:each="year : ${#numbers.sequence(currentYear-2, currentYear+1)}" 
                                            th:value="${year}" th:text="${year}" th:selected="${year == currentYear}"></option>
                                </select>
                            </li>
                            <li>
                                <p>月份：</p>
                                <select name="recordMonth" id="recordMonth" class="form-control">
                                    <option value="">请选择月份</option>
                                    <option th:each="month : ${#numbers.sequence(1, 12)}" 
                                            th:value="${month}" th:text="${month + '月'}"></option>
                                </select>
                            </li>
                            <li>
                                <p>员工姓名：</p>
                                <input type="text" name="staffName" placeholder="请输入员工姓名"/>
                            </li>
                            <li>
                                <p>状态：</p>
                                <select name="status" class="form-control">
                                    <option value="">所有状态</option>
                                    <option value="DRAFT">草稿</option>
                                    <option value="SUBMITTED">已提交</option>
                                    <option value="CALCULATED">已计算</option>
                                    <option value="APPROVED">已审批</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="system:workload:record:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="system:workload:record:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:workload:record:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-info multiple disabled" onclick="submitRecords()" shiro:hasPermission="system:workload:record:edit">
                    <i class="fa fa-paper-plane"></i> 提交
                </a>
                <a class="btn btn-warning" onclick="showCalculateModal()" shiro:hasPermission="system:workload:record:calculate">
                    <i class="fa fa-calculator"></i> 计算结算金额
                </a>
                <a class="btn btn-success multiple disabled" onclick="approveRecords()" shiro:hasPermission="system:workload:record:approve">
                    <i class="fa fa-check"></i> 审批
                </a>
                <a class="btn btn-info" onclick="showStatistics()" shiro:hasPermission="system:workload:record:query">
                    <i class="fa fa-bar-chart"></i> 统计
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="system:workload:record:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <!-- 计算结算金额模态框 -->
    <div class="modal fade" id="calculateModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">计算结算金额</h4>
                </div>
                <div class="modal-body">
                    <form id="calculateForm">
                        <div class="form-group">
                            <label>厂商：</label>
                            <select name="calcVendorId" id="calcVendorId" class="form-control" required>
                                <option value="">请选择厂商</option>
                                <option th:each="vendor : ${vendorList}" th:value="${vendor.id}" th:text="${vendor.name}"></option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>年份：</label>
                            <select name="calcYear" id="calcYear" class="form-control" required>
                                <option th:each="year : ${#numbers.sequence(currentYear-2, currentYear+1)}" 
                                        th:value="${year}" th:text="${year}" th:selected="${year == currentYear}"></option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>月份：</label>
                            <select name="calcMonth" id="calcMonth" class="form-control" required>
                                <option th:each="month : ${#numbers.sequence(1, 12)}" 
                                        th:value="${month}" th:text="${month + '月'}"></option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="executeCalculate()">开始计算</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计信息模态框 -->
    <div class="modal fade" id="statisticsModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">工作量统计</h4>
                </div>
                <div class="modal-body">
                    <form id="statisticsForm">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>厂商：</label>
                                    <select name="statVendorId" id="statVendorId" class="form-control" required>
                                        <option value="">请选择厂商</option>
                                        <option th:each="vendor : ${vendorList}" th:value="${vendor.id}" th:text="${vendor.name}"></option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>年份：</label>
                                    <select name="statYear" id="statYear" class="form-control" required>
                                        <option th:each="year : ${#numbers.sequence(currentYear-2, currentYear+1)}" 
                                                th:value="${year}" th:text="${year}" th:selected="${year == currentYear}"></option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>月份：</label>
                                    <select name="statMonth" id="statMonth" class="form-control" required>
                                        <option th:each="month : ${#numbers.sequence(1, 12)}" 
                                                th:value="${month}" th:text="${month + '月'}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <button type="button" class="btn btn-primary" onclick="loadStatistics()">查询统计</button>
                            </div>
                        </div>
                    </form>
                    <hr>
                    <div id="statisticsContent" style="display: none;">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="panel panel-primary">
                                    <div class="panel-heading">总记录数</div>
                                    <div class="panel-body">
                                        <h3 id="totalRecords">0</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="panel panel-info">
                                    <div class="panel-heading">总工作天数</div>
                                    <div class="panel-body">
                                        <h3 id="totalWorkDays">0</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="panel panel-warning">
                                    <div class="panel-heading">总人月数</div>
                                    <div class="panel-body">
                                        <h3 id="totalPersonMonths">0</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="panel panel-success">
                                    <div class="panel-heading">总结算金额</div>
                                    <div class="panel-body">
                                        <h3 id="totalAmount">¥0</h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>状态</th>
                                            <th>草稿</th>
                                            <th>已提交</th>
                                            <th>已计算</th>
                                            <th>已审批</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>数量</strong></td>
                                            <td id="draftCount">0</td>
                                            <td id="submittedCount">0</td>
                                            <td id="calculatedCount">0</td>
                                            <td id="approvedCount">0</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-select-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/workload/record";
        var editFlag = [[${@permission.hasPermi('system:workload:record:edit')}]];
        var removeFlag = [[${@permission.hasPermi('system:workload:record:remove')}]];
        var calculateFlag = [[${@permission.hasPermi('system:workload:record:calculate')}]];
        var approveFlag = [[${@permission.hasPermi('system:workload:record:approve')}]];

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "人员工作量登记",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'vendorName',
                    title: '厂商名称'
                },
                {
                    field: 'staffNo',
                    title: '员工工号'
                },
                {
                    field: 'staffName',
                    title: '员工姓名'
                },
                {
                    field: 'recordYear',
                    title: '年份'
                },
                {
                    field: 'recordMonth',
                    title: '月份',
                    formatter: function(value, row, index) {
                        return value + '月';
                    }
                },
                {
                    field: 'workDays',
                    title: '工作天数',
                    formatter: function(value, row, index) {
                        return value ? value.toFixed(2) : '0.00';
                    }
                },
                {
                    field: 'personMonths',
                    title: '人月数',
                    formatter: function(value, row, index) {
                        return value ? value.toFixed(4) : '0.0000';
                    }
                },
                {
                    field: 'unitPrice',
                    title: '单价(元/人月)',
                    formatter: function(value, row, index) {
                        return value ? '¥' + value.toFixed(2) : '¥0.00';
                    }
                },
                {
                    field: 'settlementAmount',
                    title: '结算金额',
                    formatter: function(value, row, index) {
                        return value ? '¥' + value.toFixed(2) : '¥0.00';
                    }
                },
                {
                    field: 'status',
                    title: '状态',
                    formatter: function(value, row, index) {
                        switch(value) {
                            case 'DRAFT': return '<span class="label label-default">草稿</span>';
                            case 'SUBMITTED': return '<span class="label label-info">已提交</span>';
                            case 'CALCULATED': return '<span class="label label-warning">已计算</span>';
                            case 'APPROVED': return '<span class="label label-success">已审批</span>';
                            default: return value;
                        }
                    }
                },
                {
                    field: 'workDescription',
                    title: '工作内容',
                    formatter: function(value, row, index) {
                        if (value && value.length > 20) {
                            return '<span title="' + value + '">' + value.substring(0, 20) + '...</span>';
                        }
                        return value || '';
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        if (editFlag && (row.status == 'DRAFT' || row.status == 'SUBMITTED')) {
                            actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        }
                        if (removeFlag && row.status == 'DRAFT') {
                            actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        }
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
            
            // 初始化下拉框
            $('.selectpicker').selectpicker();
        });

        // 提交记录
        function submitRecords() {
            var rows = $.table.selectColumns("id");
            if (rows.length == 0) {
                $.modal.alertWarning("请选择要提交的数据");
                return;
            }
            $.modal.confirm("确认要提交选中的 " + rows.length + " 条数据吗？", function() {
                var data = { "ids": rows.join(",") };
                $.operate.submit(prefix + "/submit", "post", "json", data);
            });
        }

        // 审批记录
        function approveRecords() {
            var rows = $.table.selectColumns("id");
            if (rows.length == 0) {
                $.modal.alertWarning("请选择要审批的数据");
                return;
            }
            $.modal.confirm("确认要审批选中的 " + rows.length + " 条数据吗？", function() {
                var data = { "ids": rows.join(",") };
                $.operate.submit(prefix + "/approve", "post", "json", data);
            });
        }

        // 显示计算模态框
        function showCalculateModal() {
            $('#calculateModal').modal('show');
        }

        // 执行计算
        function executeCalculate() {
            var vendorId = $('#calcVendorId').val();
            var year = $('#calcYear').val();
            var month = $('#calcMonth').val();
            
            if (!vendorId || !year || !month) {
                $.modal.alertWarning("请选择厂商、年份和月份");
                return;
            }
            
            $.modal.confirm("确认要计算 " + year + "年" + month + "月的结算金额吗？", function() {
                $.modal.loading("正在计算，请稍候...");
                $.post(prefix + "/calculate", {
                    vendorId: vendorId,
                    recordYear: year,
                    recordMonth: month
                }, function(result) {
                    $.modal.closeLoading();
                    if (result.code == web_status.SUCCESS) {
                        $('#calculateModal').modal('hide');
                        $.modal.alertSuccess("计算完成！<br>处理记录：" + result.data.calculatedCount + "/" + result.data.totalRecords + "<br>总金额：¥" + result.data.totalAmount.toFixed(2));
                        $.table.refresh();
                    } else {
                        $.modal.alertError(result.msg);
                    }
                });
            });
        }

        // 显示统计
        function showStatistics() {
            $('#statisticsModal').modal('show');
        }

        // 加载统计数据
        function loadStatistics() {
            var vendorId = $('#statVendorId').val();
            var year = $('#statYear').val();
            var month = $('#statMonth').val();
            
            if (!vendorId || !year || !month) {
                $.modal.alertWarning("请选择厂商、年份和月份");
                return;
            }
            
            $.post(prefix + "/statistics", {
                vendorId: vendorId,
                recordYear: year,
                recordMonth: month
            }, function(result) {
                if (result.code == web_status.SUCCESS) {
                    var data = result.data;
                    $('#totalRecords').text(data.totalRecords);
                    $('#totalWorkDays').text(data.totalWorkDays.toFixed(2));
                    $('#totalPersonMonths').text(data.totalPersonMonths.toFixed(4));
                    $('#totalAmount').text('¥' + data.totalAmount.toFixed(2));
                    $('#draftCount').text(data.draftCount);
                    $('#submittedCount').text(data.submittedCount);
                    $('#calculatedCount').text(data.calculatedCount);
                    $('#approvedCount').text(data.approvedCount);
                    $('#statisticsContent').show();
                } else {
                    $.modal.alertError(result.msg);
                }
            });
        }
    </script>
</body>
</html>
