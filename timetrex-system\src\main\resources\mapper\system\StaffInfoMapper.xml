<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.timetrex.system.mapper.StaffInfoMapper">

    <resultMap type="StaffInfo" id="StaffInfoResult">
        <result property="id"    column="id"    />
        <result property="staffNo"    column="staff_no"    />
        <result property="name"    column="name"    />
        <result property="vendorId"    column="vendor_id"    />
        <result property="projectId"    column="project_id"    />
        <result property="roleId"    column="role_id"    />
        <result property="experienceYears"    column="experience_years"    />
        <result property="unitPrice"    column="unit_price"    />
        <result property="staffLevel"    column="staff_level"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectStaffInfoVo">
        select id, staff_no, name, vendor_id, project_id, role_id, experience_years, unit_price, staff_level, status from staff_info
    </sql>

    <select id="selectStaffInfoList" parameterType="StaffInfo" resultMap="StaffInfoResult">
        <include refid="selectStaffInfoVo"/>
        <where>
            <if test="staffNo != null  and staffNo != ''"> and staff_no = #{staffNo}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="vendorId != null "> and vendor_id = #{vendorId}</if>
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="roleId != null "> and role_id = #{roleId}</if>
            <if test="experienceYears != null "> and experience_years = #{experienceYears}</if>
            <if test="unitPrice != null "> and unit_price = #{unitPrice}</if>
            <if test="staffLevel != null "> and staff_level = #{staffLevel}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectStaffInfoById" parameterType="Long" resultMap="StaffInfoResult">
        <include refid="selectStaffInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertStaffInfo" parameterType="StaffInfo" useGeneratedKeys="true" keyProperty="id">
        insert into staff_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="staffNo != null and staffNo != ''">staff_no,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="vendorId != null">vendor_id,</if>
            <if test="projectId != null">project_id,</if>
            <if test="roleId != null">role_id,</if>
            <if test="experienceYears != null">experience_years,</if>
            <if test="unitPrice != null">unit_price,</if>
            <if test="staffLevel != null">staff_level,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="staffNo != null and staffNo != ''">#{staffNo},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="vendorId != null">#{vendorId},</if>
            <if test="projectId != null">#{projectId},</if>
            <if test="roleId != null">#{roleId},</if>
            <if test="experienceYears != null">#{experienceYears},</if>
            <if test="unitPrice != null">#{unitPrice},</if>
            <if test="staffLevel != null">#{staffLevel},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateStaffInfo" parameterType="StaffInfo">
        update staff_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="staffNo != null and staffNo != ''">staff_no = #{staffNo},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="vendorId != null">vendor_id = #{vendorId},</if>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="roleId != null">role_id = #{roleId},</if>
            <if test="experienceYears != null">experience_years = #{experienceYears},</if>
            <if test="unitPrice != null">unit_price = #{unitPrice},</if>
            <if test="staffLevel != null">staff_level = #{staffLevel},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStaffInfoById" parameterType="Long">
        delete from staff_info where id = #{id}
    </delete>

    <delete id="deleteStaffInfoByIds" parameterType="String">
        delete from staff_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectStaffInfoByStaffNo" parameterType="String" resultMap="StaffInfoResult">
        <include refid="selectStaffInfoVo"/>
        where staff_no = #{staffNo}
    </select>

    <select id="selectActiveStaffByVendorId" parameterType="Long" resultMap="StaffInfoResult">
        <include refid="selectStaffInfoVo"/>
        where vendor_id = #{vendorId} and status = 1
        order by staff_no
    </select>

    <select id="selectStaffInfoByStaffName" parameterType="String" resultMap="StaffInfoResult">
        <include refid="selectStaffInfoVo"/>
        where name = #{staffName} and status = 1
        limit 1
    </select>

    <select id="selectStaffInfoListByName" parameterType="String" resultMap="StaffInfoResult">
        <include refid="selectStaffInfoVo"/>
        where name like concat('%', #{staffName}, '%') and status = 1
        order by staff_no
        limit 10
    </select>

</mapper>