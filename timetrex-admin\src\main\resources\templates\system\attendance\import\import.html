<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('考勤数据导入')" />
    <th:block th:include="include :: bootstrap-fileinput-css"/>
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <p>姓名：</p>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <p>账号：</p>
                                <input type="text" name="staffNo"/>
                            </li>
                            <li>
                                <p>日期：</p>
                                <input type="text" class="time-input" name="recordDate" placeholder="请选择日期"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="importExcel()" shiro:hasPermission="system:attendance:import:import">
                    <i class="fa fa-upload"></i> 导入
                </a>
                <!-- <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="system:attendance:import:remove">
                    <i class="fa fa-remove"></i> 删除
                </a> -->
            </div>

            <div class="col-sm-12 select-table table-striped">
                <div class="tabs-container">
                    <ul class="nav nav-tabs">
                        <li><a data-toggle="tab" href="#tab-1" aria-expanded="false">考勤概况</a></li>
                        <li class="active"><a data-toggle="tab" href="#tab-2" aria-expanded="true">打卡详情</a></li>
                    </ul>
                    <div class="tab-content">
                        <div id="tab-1" class="tab-pane">
                            <div class="panel-body">
                                <table id="bootstrap-table-summary"></table>
                            </div>
                        </div>
                        <div id="tab-2" class="tab-pane active">
                            <div class="panel-body">
                                <table id="bootstrap-table-detail"></table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="importModal" tabindex="-1" role="dialog" aria-labelledby="importModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                    <h4 class="modal-title" id="importModalLabel">导入考勤数据</h4>
                </div>
                <div class="modal-body">
                    <form id="importForm" enctype="multipart/form-data" class="mt20 mb10">
                        <div class="form-group">
                            <label class="font-noraml">导入提示</label>
                            <div class="well well-sm">
                                <ol>
                                    <li>Excel文件必须包含两个工作表：概况统计与打卡明细、打卡详情</li>
                                    <li>概况统计字段：时间、姓名、账号、部门、职务、工号、所属规则、班次、最早、最晚、打卡次数、标准工作时长、实际工作时长</li>
                                    <li>打卡详情字段：全部字段导入</li>
                                    <li>数据关联：通过账号字段关联两个sheet数据</li>
                                </ol>
                            </div>
                        </div>
                        <div class="form-group">
                            <input id="files" name="files" class="file" type="file" multiple>
                            <div class="help-block">
                                <small class="text-navy">支持同时上传最多3个Excel文件，每个文件大小不超过10MB</small>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="submitImportForm()">导入</button>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-fileinput-js"/>
    <script th:inline="javascript">
        var prefix = ctx + "system/attendance/import";
        var summaryPrefix = prefix + "/summaryList";
        var detailPrefix = prefix + "/detailList";

        $(function() {
            // 初始化考勤概况表格
            var options = {
                id: "bootstrap-table-summary",
                url: summaryPrefix,
                removeUrl: prefix + "/remove",
                modalName: "考勤概况",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'staffNo',
                    title: '账号'
                },
                {
                    field: 'name',
                    title: '姓名'
                },
                {
                    field: 'recordDate',
                    title: '日期'
                },
                {
                    field: 'dayOfWeek',
                    title: '星期几'
                },
                {
                    field: 'department',
                    title: '部门'
                },
                {
                    field: 'position',
                    title: '职务'
                },
                {
                    field: 'workRule',
                    title: '所属规则'
                },
                {
                    field: 'shift',
                    title: '班次'
                },
                {
                    field: 'earliestCheck',
                    title: '最早打卡时间'
                },
                {
                    field: 'latestCheck',
                    title: '最晚打卡时间'
                },
                {
                    field: 'checkCount',
                    title: '打卡次数'
                },
                {
                    field: 'standardHours',
                    title: '标准工作时长'
                },
                {
                    field: 'actualHours',
                    title: '实际工作时长'
                },
                {
                    field: 'calculatedHours',
                    title: '实际打卡按9小时算'
                }]
            };
            $.table.init(options);

            var detailOptions = {
                id: "bootstrap-table-detail",
                url: detailPrefix,
                removeUrl: prefix + "/remove",
                modalName: "打卡详情",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'staffNo',
                    title: '账号'
                },
                {
                    field: 'name',
                    title: '姓名'
                },
                {
                    field: 'checkDate',
                    title: '日期'
                },
                {
                    field: 'dayOfWeek',
                    title: '星期几'
                },
                {
                    field: 'department',
                    title: '部门'
                },
                {
                    field: 'position',
                    title: '职务'
                },
                {
                    field: 'workRule',
                    title: '所属规则'
                },
                {
                    field: 'checkType',
                    title: '打卡类型'
                },
                {
                    field: 'scheduledTime',
                    title: '应打卡时间'
                },
                {
                    field: 'actualTime',
                    title: '实际打卡时间'
                },
                {
                    field: 'checkStatus',
                    title: '打卡状态'
                },
                {
                    field: 'location',
                    title: '打卡地点'
                },
                {
                    field: 'deviceId',
                    title: '打卡设备'
                }]
            };
            $.table.init(detailOptions);

            // 初始化文件上传控件
            $("#files").fileinput({
                'theme': 'fa',
                'uploadUrl': '#',
                showPreview: true,
                showUpload: false,
                maxFileCount: 3,
                maxFileSize: 10240, // 10MB
                allowedFileExtensions: ['xls', 'xlsx'],
                elErrorContainer: '#errorBlock',
                msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！",
                fileActionSettings: {
                    showRemove: true,
                    showUpload: false,
                    showZoom: false,
                    showDrag: false
                }
            });

            // 默认选中打卡详情标签
            $('a[href="#tab-2"]').tab('show');
        });

        // 打开导入模态框
        function importExcel() {
            $("#importModal").modal("show");
        }

        // 提交导入表单
        function submitImportForm() {
            var formData = new FormData($("#importForm")[0]);
            var fileCount = $('#files')[0].files.length;

            if (fileCount === 0) {
                $.modal.alertWarning("请选择要导入的文件");
                return;
            }

            if (fileCount > 3) {
                $.modal.alertWarning("一次最多只能上传3个文件");
                return;
            }

            // 显示进度条
            $.modal.loading("正在导入数据，请稍候...");

            $.ajax({
                url: prefix + "/importData",
                type: 'POST',
                data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success: function(result) {
                    $.modal.closeLoading();

                    if (result.code == web_status.SUCCESS) {
                        $.modal.closeAll();
                        $.modal.alertSuccess(result.msg);

                        // 刷新两个表格
                        $("#bootstrap-table-summary").bootstrapTable('refresh');
                        $("#bootstrap-table-detail").bootstrapTable('refresh');
                    } else {
                        $.modal.alertError(result.msg);
                    }
                },
                error: function(xhr, status, error) {
                    $.modal.closeLoading();
                    $.modal.alertError("导入失败：" + error);
                }
            });
        }
    </script>
</body>
</html>
