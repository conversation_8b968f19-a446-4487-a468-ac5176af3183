package com.timetrex.system.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Calendar;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.timetrex.common.utils.DateUtils;
import com.timetrex.system.mapper.StaffWorkloadRecordMapper;
import com.timetrex.system.mapper.StaffUnitPriceMapper;
import com.timetrex.system.mapper.VendorMapper;
import com.timetrex.system.domain.StaffWorkloadRecord;
import com.timetrex.system.domain.StaffUnitPrice;
import com.timetrex.system.domain.Vendor;
import com.timetrex.system.service.IStaffWorkloadRecordService;

/**
 * 人员工作量登记Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-20
 */
@Service
public class StaffWorkloadRecordServiceImpl implements IStaffWorkloadRecordService 
{
    @Autowired
    private StaffWorkloadRecordMapper staffWorkloadRecordMapper;
    
    @Autowired
    private StaffUnitPriceMapper staffUnitPriceMapper;
    
    @Autowired
    private VendorMapper vendorMapper;

    /**
     * 查询人员工作量登记
     * 
     * @param id 人员工作量登记主键
     * @return 人员工作量登记
     */
    @Override
    public StaffWorkloadRecord selectStaffWorkloadRecordById(Long id)
    {
        return staffWorkloadRecordMapper.selectStaffWorkloadRecordById(id);
    }

    /**
     * 查询人员工作量登记列表
     * 
     * @param staffWorkloadRecord 人员工作量登记
     * @return 人员工作量登记
     */
    @Override
    public List<StaffWorkloadRecord> selectStaffWorkloadRecordList(StaffWorkloadRecord staffWorkloadRecord)
    {
        return staffWorkloadRecordMapper.selectStaffWorkloadRecordList(staffWorkloadRecord);
    }

    /**
     * 新增人员工作量登记
     * 
     * @param staffWorkloadRecord 人员工作量登记
     * @return 结果
     */
    @Override
    public int insertStaffWorkloadRecord(StaffWorkloadRecord staffWorkloadRecord)
    {
        // 设置默认状态为草稿
        if (staffWorkloadRecord.getStatus() == null) {
            staffWorkloadRecord.setStatus("DRAFT");
        }
        
        // 设置厂商名称
        if (staffWorkloadRecord.getVendorId() != null) {
            Vendor vendor = vendorMapper.selectVendorById(staffWorkloadRecord.getVendorId());
            if (vendor != null) {
                staffWorkloadRecord.setVendorName(vendor.getName());
            }
        }
        
        staffWorkloadRecord.setCreateTime(DateUtils.getNowDate());
        return staffWorkloadRecordMapper.insertStaffWorkloadRecord(staffWorkloadRecord);
    }

    /**
     * 修改人员工作量登记
     * 
     * @param staffWorkloadRecord 人员工作量登记
     * @return 结果
     */
    @Override
    public int updateStaffWorkloadRecord(StaffWorkloadRecord staffWorkloadRecord)
    {
        // 设置厂商名称
        if (staffWorkloadRecord.getVendorId() != null) {
            Vendor vendor = vendorMapper.selectVendorById(staffWorkloadRecord.getVendorId());
            if (vendor != null) {
                staffWorkloadRecord.setVendorName(vendor.getName());
            }
        }
        
        staffWorkloadRecord.setUpdateTime(DateUtils.getNowDate());
        return staffWorkloadRecordMapper.updateStaffWorkloadRecord(staffWorkloadRecord);
    }

    /**
     * 批量删除人员工作量登记
     * 
     * @param ids 需要删除的人员工作量登记主键
     * @return 结果
     */
    @Override
    public int deleteStaffWorkloadRecordByIds(Long[] ids)
    {
        // 检查是否可以删除
        for (Long id : ids) {
            if (!canDelete(id)) {
                throw new RuntimeException("存在已提交或已计算的记录，无法删除");
            }
        }
        return staffWorkloadRecordMapper.deleteStaffWorkloadRecordByIds(ids);
    }

    /**
     * 删除人员工作量登记信息
     * 
     * @param id 人员工作量登记主键
     * @return 结果
     */
    @Override
    public int deleteStaffWorkloadRecordById(Long id)
    {
        if (!canDelete(id)) {
            throw new RuntimeException("已提交或已计算的记录无法删除");
        }
        return staffWorkloadRecordMapper.deleteStaffWorkloadRecordById(id);
    }

    /**
     * 提交工作量登记
     * 
     * @param ids 主键集合
     * @return 结果
     */
    @Override
    @Transactional
    public int submitWorkloadRecords(Long[] ids)
    {
        // 检查状态
        for (Long id : ids) {
            StaffWorkloadRecord record = staffWorkloadRecordMapper.selectStaffWorkloadRecordById(id);
            if (record == null || !"DRAFT".equals(record.getStatus())) {
                throw new RuntimeException("只能提交草稿状态的记录");
            }
        }
        
        // 批量更新状态为已提交
        int result = staffWorkloadRecordMapper.batchUpdateStatus(ids, "SUBMITTED", null);
        
        // 更新提交时间
        for (Long id : ids) {
            StaffWorkloadRecord record = new StaffWorkloadRecord();
            record.setId(id);
            record.setSubmitTime(new Date());
            staffWorkloadRecordMapper.updateStaffWorkloadRecord(record);
        }
        
        return result;
    }

    /**
     * 计算工作量结算金额
     * 
     * @param vendorId 厂商ID
     * @param recordYear 年份
     * @param recordMonth 月份
     * @param calculateUser 计算人员
     * @return 计算结果
     */
    @Override
    @Transactional
    public Map<String, Object> calculateSettlementAmount(Long vendorId, Integer recordYear, Integer recordMonth, String calculateUser)
    {
        Map<String, Object> result = new HashMap<>();
        
        // 获取厂商信息
        Vendor vendor = vendorMapper.selectVendorById(vendorId);
        if (vendor == null) {
            throw new RuntimeException("厂商不存在");
        }
        
        // 获取平均计薪工作日
        BigDecimal avgWorkDays = vendor.getAvgWorkDaysPerMonth();
        if (avgWorkDays == null || avgWorkDays.compareTo(BigDecimal.ZERO) <= 0) {
            avgWorkDays = new BigDecimal("22.00"); // 默认22天
        }
        
        // 获取待计算的工作量登记
        List<StaffWorkloadRecord> records = staffWorkloadRecordMapper.selectPendingCalculation(vendorId, recordYear, recordMonth);
        
        int calculatedCount = 0;
        BigDecimal totalAmount = BigDecimal.ZERO;
        
        // 构造目标日期（用于查询单价）
        Calendar calendar = Calendar.getInstance();
        calendar.set(recordYear, recordMonth - 1, 1);
        Date targetDate = calendar.getTime();
        
        for (StaffWorkloadRecord record : records) {
            try {
                // 计算人月数 = 工作天数 / 平均计薪工作日
                BigDecimal personMonths = record.getWorkDays().divide(avgWorkDays, 4, RoundingMode.HALF_UP);
                record.setPersonMonths(personMonths);
                
                // 获取人员单价
                StaffUnitPrice unitPrice = staffUnitPriceMapper.selectEffectivePriceByStaffAndDate(record.getStaffNo(), targetDate);
                if (unitPrice != null) {
                    record.setUnitPrice(unitPrice.getUnitPrice());
                    
                    // 计算结算金额 = 人月数 * 单价
                    BigDecimal settlementAmount = personMonths.multiply(unitPrice.getUnitPrice()).setScale(2, RoundingMode.HALF_UP);
                    record.setSettlementAmount(settlementAmount);
                    totalAmount = totalAmount.add(settlementAmount);
                } else {
                    // 没有配置单价，设置为0
                    record.setUnitPrice(BigDecimal.ZERO);
                    record.setSettlementAmount(BigDecimal.ZERO);
                }
                
                // 更新状态和计算信息
                record.setStatus("CALCULATED");
                record.setCalculateTime(new Date());
                record.setCalculateUser(calculateUser);
                
                staffWorkloadRecordMapper.updateStaffWorkloadRecord(record);
                calculatedCount++;
                
            } catch (Exception e) {
                // 记录计算失败的情况，但不中断整个流程
                System.err.println("计算工作量失败，员工：" + record.getStaffName() + "，错误：" + e.getMessage());
            }
        }
        
        result.put("success", true);
        result.put("calculatedCount", calculatedCount);
        result.put("totalRecords", records.size());
        result.put("totalAmount", totalAmount);
        result.put("avgWorkDays", avgWorkDays);
        
        return result;
    }

    /**
     * 审批工作量登记
     * 
     * @param ids 主键集合
     * @param approveUser 审批人员
     * @return 结果
     */
    @Override
    @Transactional
    public int approveWorkloadRecords(Long[] ids, String approveUser)
    {
        // 检查状态
        for (Long id : ids) {
            StaffWorkloadRecord record = staffWorkloadRecordMapper.selectStaffWorkloadRecordById(id);
            if (record == null || !"CALCULATED".equals(record.getStatus())) {
                throw new RuntimeException("只能审批已计算状态的记录");
            }
        }
        
        // 批量更新状态为已审批
        int result = staffWorkloadRecordMapper.batchUpdateStatus(ids, "APPROVED", approveUser);
        
        // 更新审批时间
        for (Long id : ids) {
            StaffWorkloadRecord record = new StaffWorkloadRecord();
            record.setId(id);
            record.setApproveTime(new Date());
            record.setApproveUser(approveUser);
            staffWorkloadRecordMapper.updateStaffWorkloadRecord(record);
        }
        
        return result;
    }

    /**
     * 根据厂商ID和年月查询工作量登记
     * 
     * @param vendorId 厂商ID
     * @param recordYear 年份
     * @param recordMonth 月份
     * @return 工作量登记列表
     */
    @Override
    public List<StaffWorkloadRecord> selectByVendorAndMonth(Long vendorId, Integer recordYear, Integer recordMonth)
    {
        return staffWorkloadRecordMapper.selectByVendorAndMonth(vendorId, recordYear, recordMonth);
    }

    /**
     * 检查是否可以删除工作量登记
     * 
     * @param id 主键
     * @return 是否可以删除
     */
    @Override
    public boolean canDelete(Long id)
    {
        StaffWorkloadRecord record = staffWorkloadRecordMapper.selectStaffWorkloadRecordById(id);
        return record != null && "DRAFT".equals(record.getStatus());
    }

    /**
     * 检查是否可以修改工作量登记
     * 
     * @param id 主键
     * @return 是否可以修改
     */
    @Override
    public boolean canEdit(Long id)
    {
        StaffWorkloadRecord record = staffWorkloadRecordMapper.selectStaffWorkloadRecordById(id);
        return record != null && ("DRAFT".equals(record.getStatus()) || "SUBMITTED".equals(record.getStatus()));
    }

    /**
     * 获取工作量统计数据
     * 
     * @param vendorId 厂商ID
     * @param recordYear 年份
     * @param recordMonth 月份
     * @return 统计数据
     */
    @Override
    public Map<String, Object> getWorkloadStatistics(Long vendorId, Integer recordYear, Integer recordMonth)
    {
        Map<String, Object> result = new HashMap<>();
        
        List<StaffWorkloadRecord> records = staffWorkloadRecordMapper.selectByVendorAndMonth(vendorId, recordYear, recordMonth);
        
        int totalRecords = records.size();
        int draftCount = 0;
        int submittedCount = 0;
        int calculatedCount = 0;
        int approvedCount = 0;
        
        BigDecimal totalWorkDays = BigDecimal.ZERO;
        BigDecimal totalPersonMonths = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        
        for (StaffWorkloadRecord record : records) {
            switch (record.getStatus()) {
                case "DRAFT":
                    draftCount++;
                    break;
                case "SUBMITTED":
                    submittedCount++;
                    break;
                case "CALCULATED":
                    calculatedCount++;
                    break;
                case "APPROVED":
                    approvedCount++;
                    break;
            }
            
            if (record.getWorkDays() != null) {
                totalWorkDays = totalWorkDays.add(record.getWorkDays());
            }
            if (record.getPersonMonths() != null) {
                totalPersonMonths = totalPersonMonths.add(record.getPersonMonths());
            }
            if (record.getSettlementAmount() != null) {
                totalAmount = totalAmount.add(record.getSettlementAmount());
            }
        }
        
        result.put("totalRecords", totalRecords);
        result.put("draftCount", draftCount);
        result.put("submittedCount", submittedCount);
        result.put("calculatedCount", calculatedCount);
        result.put("approvedCount", approvedCount);
        result.put("totalWorkDays", totalWorkDays);
        result.put("totalPersonMonths", totalPersonMonths);
        result.put("totalAmount", totalAmount);
        
        return result;
    }
}
