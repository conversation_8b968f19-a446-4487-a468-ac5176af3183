获取打卡详情数据

返回参数：

| 参数             | 说明                                                         |
| ---------------- | ------------------------------------------------------------ |
| userid           | 用户id                                                       |
| groupname        | 打卡规则名称                                                 |
| checkin_type     | 打卡类型。字符串，目前有：上班打卡，下班打卡，外出打卡，仅记录打卡时间和位置 |
| exception_type   | 异常类型，字符串，包括：时间异常，地点异常，未打卡，wifi异常，非常用设备。如果有多个异常，以分号间隔 |
| checkin_time     | 打卡时间。Unix时间戳                                         |
| location_title   | 打卡地点title                                                |
| location_detail  | 打卡地点详情                                                 |
| wifiname         | 打卡wifi名称                                                 |
| notes            | 打卡备注                                                     |
| wifimac          | 打卡的MAC地址/bssid                                          |
| mediaids         | 打卡的附件media_id，可使用media/get获取附件                  |
| lat              | 位置打卡地点纬度，是实际纬度的1000000倍，与腾讯地图一致采用GCJ-02坐标系统标准 |
| lng              | 位置打卡地点经度，是实际经度的1000000倍，与腾讯地图一致采用GCJ-02坐标系统标准 |
| deviceid         | 打卡设备id                                                   |
| sch_checkin_time | 标准打卡时间，指此次打卡时间对应的标准上班时间或标准下班时间 |
| groupid          | 规则id，表示打卡记录所属规则的id                             |
| schedule_id      | 班次id，表示打卡记录所属规则中，所属班次的id                 |
| timeline_id      | 时段id，表示打卡记录所属规则中，某一班次中的某一时段的id，如上下班时间为9:00-12:00、13:00-18:00的班次中，9:00-12:00为其中一组时段 |

样例报文：

```
{
   "errcode":0,
   "errmsg":"ok",
   "checkindata": [{
		"userid" : "james",
		"groupname" : "打卡一组",         
		"checkin_type" : "上班打卡",      
		"exception_type" : "地点异常",   
		"checkin_time" : 1492617610,  
		"location_title" : "依澜府",    
		"location_detail" : "四川省成都市武侯区益州大道中段784号附近",  
		"wifiname" : "办公一区",         
		"notes" : "路上堵车，迟到了5分钟",
		"wifimac" : "3c:46:d8:0c:7a:70",
		"mediaids":["WWCISP_G8PYgRaOVHjXWUWFqchpBqqqUpGj0OyR9z6WTwhnMZGCPHxyviVstiv_2fTG8YOJq8L8zJT2T2OvTebANV-2MQ"],
		"sch_checkin_time" : 1492617610,
		"groupid" : 1,
		"schedule_id" : 0,
		"timeline_id" : 2
	},{
		"userid" : "paul",
		"groupname" : "打卡二组",         
		"checkin_type" : "外出打卡",      
		"exception_type" : "时间异常",   
		"checkin_time" : 1492617620,  
		"location_title" : "重庆出口加工区",    
		"location_detail" : "重庆市渝北区金渝大道101号金渝大道",  
		"wifiname" : "办公室二区",         
		"notes" : "",
		"wifimac" : "3c:46:d8:0c:7a:71",
		"mediaids":["WWCISP_G8PYgRaOVHjXWUWFqchpBqqqUpGj0OyR9z6WTwhnMZGCPHxyviVstiv_2fTG8YOJq8L8zJT2T2OvTebANV-2MQ"],
		"lat": 30547645,
        "lng": 104063236,
		"deviceid":"E5FA89F6-3926-4972-BE4F-4A7ACF4701E2",
		"sch_checkin_time" : 1492617610,
		"groupid" : 2,
		"schedule_id" : 3,
		"timeline_id" : 1
	}]
}
```

