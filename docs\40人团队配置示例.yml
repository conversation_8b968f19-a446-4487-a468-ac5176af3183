# 40人团队企业微信打卡数据同步配置示例

wechat:
  corpid: ww193cf34f5ed70135
  checkin:
    secret: OL44F7Ik4QhMrqfPOG6rxoxIJNFaPX_OKpNypcAaGq0
  sync:
    # 40人的用户ID列表示例
    userIds:
      # 技术部门 (15人)
      - tech001    # 张三 - 技术总监
      - tech002    # 李四 - 前端开发
      - tech003    # 王五 - 后端开发
      - tech004    # 赵六 - 全栈开发
      - tech005    # 钱七 - 测试工程师
      - tech006    # 孙八 - 运维工程师
      - tech007    # 周九 - 产品经理
      - tech008    # 吴十 - UI设计师
      - tech009    # 郑一 - 架构师
      - tech010    # 王二 - 数据库管理员
      - tech011    # 李三 - 安全工程师
      - tech012    # 张四 - 移动端开发
      - tech013    # 赵五 - DevOps工程师
      - tech014    # 钱六 - 技术文档
      - tech015    # 孙七 - 实习生
      
      # 销售部门 (12人)
      - sales001   # 陈一 - 销售总监
      - sales002   # 林二 - 大客户经理
      - sales003   # 黄三 - 渠道经理
      - sales004   # 刘四 - 销售代表
      - sales005   # 杨五 - 销售代表
      - sales006   # 朱六 - 销售代表
      - sales007   # 徐七 - 销售助理
      - sales008   # 马八 - 商务经理
      - sales009   # 胡九 - 市场专员
      - sales010   # 郭十 - 客户服务
      - sales011   # 何一 - 销售培训
      - sales012   # 高二 - 销售实习生
      
      # 运营部门 (8人)
      - ops001     # 梁一 - 运营总监
      - ops002     # 宋二 - 内容运营
      - ops003     # 唐三 # 用户运营
      - ops004     # 冯四 - 活动运营
      - ops005     # 陆五 - 数据分析
      - ops006     # 丁六 - 社群运营
      - ops007     # 袁七 - 新媒体运营
      - ops008     # 夏八 - 运营助理
      
      # 行政人事部门 (5人)
      - hr001      # 蒋一 - 人事总监
      - hr002      # 韩二 - 招聘专员
      - hr003      # 魏三 - 薪酬福利
      - hr004      # 曹四 - 行政助理
      - hr005      # 邓五 - 培训专员
    
    # 针对40人团队的优化配置
    batchSize: 8          # 每批处理8个用户（40/8=5批）
    dbBatchSize: 400      # 数据库批量插入400条
    requestInterval: 1500 # API请求间隔1.5秒，避免频率限制

# 40人团队一个月数据量估算：
# - 概况数据：40人 × 22工作日 = 880条
# - 详情数据：40人 × 22工作日 × 2次打卡 = 1760条
# - 总计：约2640条数据
#
# 分批处理策略：
# - 概况数据：分3批（400+400+80）
# - 详情数据：分5批（400×4+160）
# - 总处理时间：约20-45秒
#
# 性能优化效果：
# - 内存使用：每批最多400条数据，内存占用可控
# - 数据库压力：分批插入，避免长时间锁表
# - 网络优化：合理的请求间隔，避免API限流
# - 错误恢复：单批失败不影响其他批次
