package com.timetrex.web.controller.system;

import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.timetrex.common.annotation.Log;
import com.timetrex.common.enums.BusinessType;
import com.timetrex.system.domain.StaffInfo;
import com.timetrex.system.service.IStaffInfoService;
import com.timetrex.common.core.controller.BaseController;
import com.timetrex.common.core.domain.AjaxResult;
import com.timetrex.common.utils.poi.ExcelUtil;
import com.timetrex.common.core.page.TableDataInfo;

/**
 * 人员信息Controller
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Controller
@RequestMapping("/system/staff_info")
public class StaffInfoController extends BaseController
{
    private String prefix = "system/staff_info";

    @Autowired
    private IStaffInfoService staffInfoService;

    @RequiresPermissions("system:staff_info:view")
    @GetMapping()
    public String staff_info()
    {
        return prefix + "/staff_info";
    }

    /**
     * 查询人员信息列表
     */
    @RequiresPermissions("system:staff_info:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(StaffInfo staffInfo)
    {
        startPage();
        List<StaffInfo> list = staffInfoService.selectStaffInfoList(staffInfo);
        return getDataTable(list);
    }

    /**
     * 导出人员信息列表
     */
    @RequiresPermissions("system:staff_info:export")
    @Log(title = "人员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(StaffInfo staffInfo)
    {
        List<StaffInfo> list = staffInfoService.selectStaffInfoList(staffInfo);
        ExcelUtil<StaffInfo> util = new ExcelUtil<StaffInfo>(StaffInfo.class);
        return util.exportExcel(list, "人员信息数据");
    }

    /**
     * 根据工号获取人员信息
     */
    @GetMapping("/getByStaffNo")
    @ResponseBody
    public AjaxResult getByStaffNo(String staffNo)
    {
        StaffInfo staffInfo = staffInfoService.selectStaffInfoByStaffNo(staffNo);
        return AjaxResult.success(staffInfo);
    }

    /**
     * 新增人员信息
     */
    @RequiresPermissions("system:staff_info:add")
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存人员信息
     */
    @RequiresPermissions("system:staff_info:add")
    @Log(title = "人员信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(StaffInfo staffInfo)
    {
        return toAjax(staffInfoService.insertStaffInfo(staffInfo));
    }

    /**
     * 修改人员信息
     */
    @RequiresPermissions("system:staff_info:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        StaffInfo staffInfo = staffInfoService.selectStaffInfoById(id);
        mmap.put("staffInfo", staffInfo);
        return prefix + "/edit";
    }

    /**
     * 修改保存人员信息
     */
    @RequiresPermissions("system:staff_info:edit")
    @Log(title = "人员信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(StaffInfo staffInfo)
    {
        return toAjax(staffInfoService.updateStaffInfo(staffInfo));
    }

    /**
     * 删除人员信息
     */
    @RequiresPermissions("system:staff_info:remove")
    @Log(title = "人员信息", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(staffInfoService.deleteStaffInfoByIds(ids));
    }
}
