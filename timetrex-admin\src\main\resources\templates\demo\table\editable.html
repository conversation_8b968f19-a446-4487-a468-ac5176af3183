<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
	<th:block th:include="include :: header('表格行内编辑')" />
	<th:block th:include="include :: bootstrap-editable-css" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="btn-group-sm" id="toolbar" role="group">
             <a class="btn btn-info" onclick="getSelections()">
	            <i class="fa fa-search"></i> 查询选择数据
	        </a>
	        <a class="btn btn-primary" onclick="getData()">
	            <i class="fa fa-search"></i> 获取所有数据
	        </a>
        </div>
		<div class="row">
			<div class="col-sm-12 select-table table-striped">
				<table id="bootstrap-table"></table>
			</div>
		</div>
	</div>
	
	<th:block th:include="include :: footer" />
	<th:block th:include="include :: bootstrap-table-editable-js" />
	
    <script th:inline="javascript">
        var prefix = ctx + "demo/table";
        var datas = [[${@dict.getType('sys_normal_disable')}]];

        $(function() {
            var options = {
                url: prefix + "/list",
		        showSearch: false,
		        showRefresh: false,
		        showToggle: false,
		        showColumns: false,
		        showPageGo: true,
		        onEditableSave: onEditableSave,
                columns: [{
		            checkbox: true
		        },
				{
					field : 'userId', 
					title : '用户ID'
				},
				{
					field : 'userCode', 
					title : '用户编号',
					editable: true
				},
				{
					field : 'userName', 
					title : '用户姓名',
					editable : {
						type : 'text',
						title : '名称',
						emptytext : "【名称】为空",
						validate : function(value) {
							if (value.length > 30) {
								return '名称不能超过30个字符';
							}
							if (value.length == 0) {
								return '名称不能为空';
							}
						}
					}
				},
				{
					field : 'userPhone', 
					title : '用户手机'
				},
				{
					field : 'userEmail', 
					title : '用户邮箱'
				},
				{
				    field : 'userBalance',
				    title : '用户余额'
				},
				{
                    field: 'status',
                    title: '用户状态',
                    align: 'center',
                    editable : {
						type : 'select',
						title : '状态',
						source : [{
							value : 0,
							text : "正常"
						}, {
							value : 1,
							text : "停用"
						}]
					}
                },
		        {
		            title: '操作',
		            align: 'center',
		            formatter: function(value, row, index) {
		            	var actions = [];
		            	actions.push('<a class="btn btn-success btn-xs" href="javascript:;"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs" href="javascript:;"><i class="fa fa-remove"></i>删除</a>');
						return actions.join('');
		            }
		        }]
            };
            $.table.init(options);
        });
        
        function onEditableSave (field, row, rowIndex, oldValue, $el) {
        	alert("字段名：" + field + "，当前值：" + row[field]  + "，旧值：" + oldValue);
        }
        
        /* 查询表格所有数据值 */
        function getData(){
        	var data = $("#" + table.options.id).bootstrapTable('getData');
            alert(JSON.stringify(data))
        }
        
        /* 查询表格选择行数据值 */
        function getSelections(){
        	var data = $("#" + table.options.id).bootstrapTable('getSelections');
        	alert(JSON.stringify(data))
        }
    </script>
</body>
</html>