package com.timetrex.common.utils;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 内存存储工具类
 */
public class MemoryBank {
    private static final Logger logger = LoggerFactory.getLogger(MemoryBank.class);
    
    // 存储数据
    private static final ConcurrentMap<String, CacheItem> store = new ConcurrentHashMap<>();
    
    // 统计信息
    private static final AtomicLong totalPuts = new AtomicLong(0);
    private static final AtomicLong totalGets = new AtomicLong(0);
    private static final AtomicLong totalHits = new AtomicLong(0);
    private static final AtomicLong totalMisses = new AtomicLong(0);
    private static final AtomicLong totalEvictions = new AtomicLong(0);
    
    /**
     * 存储数据项
     */
    private static class CacheItem {
        private final Object value;
        private final long expireTime;
        
        CacheItem(Object value, long expireTime) {
            this.value = value;
            this.expireTime = expireTime;
        }
        
        boolean isExpired() {
            return expireTime > 0 && System.currentTimeMillis() > expireTime;
        }
    }
    
    /**
     * 存入数据(永不过期)
     */
    public static void put(String key, Object value) {
        put(key, value, -1, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 存入数据(带过期时间)
     */
    public static void put(String key, Object value, long duration, TimeUnit unit) {
        if (key == null || value == null) {
            throw new IllegalArgumentException("Key and value cannot be null");
        }
        
        long expireTime = duration > 0 ? System.currentTimeMillis() + unit.toMillis(duration) : -1;
        store.put(key, new CacheItem(value, expireTime));
        totalPuts.incrementAndGet();
    }
    
    /**
     * 获取数据
     */
    public static Object get(String key) {
        CacheItem item = store.get(key);
        if (item == null) {
            totalMisses.incrementAndGet();
            return null;
        }
        
        if (item.isExpired()) {
            store.remove(key);
            totalEvictions.incrementAndGet();
            totalMisses.incrementAndGet();
            return null;
        }
        
        totalGets.incrementAndGet();
        totalHits.incrementAndGet();
        return item.value;
    }
    
    /**
     * 移除数据
     */
    public static void remove(String key) {
        store.remove(key);
    }
    
    /**
     * 清理过期数据
     */
    public static void cleanup() {
        int count = 0;
        for (String key : store.keySet()) {
            CacheItem item = store.get(key);
            if (item != null && item.isExpired()) {
                store.remove(key);
                count++;
                totalEvictions.incrementAndGet();
            }
        }
        logger.debug("Cleaned up {} expired items", count);
    }
    
    /**
     * 获取统计信息
     */
    public static Stats getStats() {
        return new Stats(
            totalGets.get(),
            totalPuts.get(),
            totalHits.get(),
            totalMisses.get(),
            totalEvictions.get(),
            store.size()
        );
    }
    
    /**
     * 统计信息类
     */
    public static class Stats {
        public final long totalGets;
        public final long totalPuts;
        public final long totalHits;
        public final long totalMisses;
        public final long totalEvictions;
        public final int currentSize;
        
        Stats(long totalGets, long totalPuts, long totalHits, 
              long totalMisses, long totalEvictions, int currentSize) {
            this.totalGets = totalGets;
            this.totalPuts = totalPuts;
            this.totalHits = totalHits;
            this.totalMisses = totalMisses;
            this.totalEvictions = totalEvictions;
            this.currentSize = currentSize;
        }
        
        public double getHitRate() {
            return totalGets == 0 ? 0 : (double) totalHits / totalGets;
        }
    }
}