<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.timetrex.system.mapper.AttendanceDetailMapper">

    <resultMap type="AttendanceDetail" id="AttendanceDetailResult">
        <result property="id"    column="id"    />
        <result property="staffNo"    column="staff_no"    />
        <result property="name"    column="name"    />
        <result property="checkDate"    column="check_date"    />
        <result property="department"    column="department"    />
        <result property="position"    column="position"    />
        <result property="workRule"    column="work_rule"    />
        <result property="checkType"    column="check_type"    />
        <result property="scheduledTime"    column="scheduled_time"    />
        <result property="actualTime"    column="actual_time"    />
        <result property="checkStatus"    column="check_status"    />
        <result property="location"    column="location"    />
        <result property="deviceId"    column="device_id"    />
        <result property="remark"    column="remark"    />
        <result property="remarkImage"    column="remark_image"    />
        <result property="leaveApplication"    column="leave_application"    />
        <result property="dayOfWeek"    column="day_of_week"    />
    </resultMap>

    <sql id="selectAttendanceDetailVo">
        select id, staff_no, name, check_date, department, position, work_rule, check_type, scheduled_time, actual_time, check_status, location, device_id, remark, remark_image, leave_application, day_of_week from attendance_detail
    </sql>

    <select id="selectAttendanceDetailList" parameterType="AttendanceDetail" resultMap="AttendanceDetailResult">
        <include refid="selectAttendanceDetailVo"/>
        <where>
            <if test="staffNo != null  and staffNo != ''"> and staff_no = #{staffNo}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="checkDate != null "> and check_date = #{checkDate}</if>
            <if test="department != null  and department != ''"> and department = #{department}</if>
            <if test="position != null  and position != ''"> and position = #{position}</if>
            <if test="workRule != null  and workRule != ''"> and work_rule = #{workRule}</if>
            <if test="checkType != null  and checkType != ''"> and check_type = #{checkType}</if>
            <if test="checkStatus != null  and checkStatus != ''"> and check_status = #{checkStatus}</if>
            <if test="deviceId != null  and deviceId != ''"> and device_id = #{deviceId}</if>
            <if test="dayOfWeek != null  and dayOfWeek != ''"> and day_of_week = #{dayOfWeek}</if>
        </where>
    </select>

    <select id="selectAttendanceDetailById" parameterType="Long" resultMap="AttendanceDetailResult">
        <include refid="selectAttendanceDetailVo"/>
        where id = #{id}
    </select>

    <select id="checkDeviceUsedByMultipleStaff" parameterType="String" resultType="int">
        select count(distinct staff_no) from attendance_detail where device_id = #{deviceId}
    </select>

    <insert id="insertAttendanceDetail" parameterType="AttendanceDetail" useGeneratedKeys="true" keyProperty="id">
        insert into attendance_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="staffNo != null">staff_no,</if>
            <if test="name != null">name,</if>
            <if test="checkDate != null">check_date,</if>
            <if test="department != null">department,</if>
            <if test="position != null">position,</if>
            <if test="workRule != null">work_rule,</if>
            <if test="checkType != null">check_type,</if>
            <if test="scheduledTime != null">scheduled_time,</if>
            <if test="actualTime != null">actual_time,</if>
            <if test="checkStatus != null">check_status,</if>
            <if test="location != null">location,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="remark != null">remark,</if>
            <if test="remarkImage != null">remark_image,</if>
            <if test="leaveApplication != null">leave_application,</if>
            <if test="dayOfWeek != null">day_of_week,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="staffNo != null">#{staffNo},</if>
            <if test="name != null">#{name},</if>
            <if test="checkDate != null">#{checkDate},</if>
            <if test="department != null">#{department},</if>
            <if test="position != null">#{position},</if>
            <if test="workRule != null">#{workRule},</if>
            <if test="checkType != null">#{checkType},</if>
            <if test="scheduledTime != null">#{scheduledTime},</if>
            <if test="actualTime != null">#{actualTime},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
            <if test="location != null">#{location},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="remarkImage != null">#{remarkImage},</if>
            <if test="leaveApplication != null">#{leaveApplication},</if>
            <if test="dayOfWeek != null">#{dayOfWeek},</if>
         </trim>
    </insert>

    <insert id="batchInsertAttendanceDetail" parameterType="java.util.List">
        insert into attendance_detail (
            staff_no, name, check_date, department, position, work_rule, check_type,
            scheduled_time, actual_time, check_status, location, device_id, remark,
            remark_image, leave_application, day_of_week
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.staffNo}, #{item.name}, #{item.checkDate}, #{item.department},
                #{item.position}, #{item.workRule}, #{item.checkType}, #{item.scheduledTime},
                #{item.actualTime}, #{item.checkStatus}, #{item.location}, #{item.deviceId},
                #{item.remark}, #{item.remarkImage}, #{item.leaveApplication}, #{item.dayOfWeek}
            )
        </foreach>
    </insert>

    <update id="updateAttendanceDetail" parameterType="AttendanceDetail">
        update attendance_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="staffNo != null">staff_no = #{staffNo},</if>
            <if test="name != null">name = #{name},</if>
            <if test="checkDate != null">check_date = #{checkDate},</if>
            <if test="department != null">department = #{department},</if>
            <if test="position != null">position = #{position},</if>
            <if test="workRule != null">work_rule = #{workRule},</if>
            <if test="checkType != null">check_type = #{checkType},</if>
            <if test="scheduledTime != null">scheduled_time = #{scheduledTime},</if>
            <if test="actualTime != null">actual_time = #{actualTime},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="location != null">location = #{location},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="remarkImage != null">remark_image = #{remarkImage},</if>
            <if test="leaveApplication != null">leave_application = #{leaveApplication},</if>
            <if test="dayOfWeek != null">day_of_week = #{dayOfWeek},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAttendanceDetailById" parameterType="Long">
        delete from attendance_detail where id = #{id}
    </delete>

    <delete id="deleteAttendanceDetailByIds" parameterType="String">
        delete from attendance_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
