package com.timetrex.web.controller.system;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.timetrex.common.annotation.Log;
import com.timetrex.common.core.controller.BaseController;
import com.timetrex.common.core.domain.AjaxResult;
import com.timetrex.common.core.page.TableDataInfo;
import com.timetrex.common.enums.BusinessType;
import com.timetrex.common.utils.poi.ExcelUtil;
import com.timetrex.system.domain.QuarterlyReport;
import com.timetrex.system.domain.Vendor;
import com.timetrex.system.service.IQuarterlyReportService;
import com.timetrex.system.service.IVendorService;

/**
 * 季度归总报表Controller
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Controller
@RequestMapping("/system/quarterly/report")
public class QuarterlyReportController extends BaseController
{
    private String prefix = "system/quarterly/report";

    @Autowired
    private IQuarterlyReportService quarterlyReportService;

    @Autowired
    private IVendorService vendorService;

    @RequiresPermissions("system:quarterly:report:view")
    @GetMapping()
    public String report(ModelMap mmap)
    {
        // 获取厂商列表
        List<Vendor> vendorList = vendorService.selectVendorList(new Vendor());
        mmap.put("vendorList", vendorList);

        // 获取当前年份
        int currentYear = java.time.LocalDate.now().getYear();
        List<Integer> yearList = new ArrayList<>();
        for (int i = currentYear - 5; i <= currentYear + 1; i++) {
            yearList.add(i);
        }
        mmap.put("yearList", yearList);
        mmap.put("currentYear", currentYear);

        return prefix + "/report";
    }

    /**
     * 查询季度归总报表
     */
    @RequiresPermissions("system:quarterly:report:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(@RequestParam(value = "year", required = false) Integer year,
                             @RequestParam(value = "quarter", required = false) Integer quarter,
                             @RequestParam(value = "vendorIds", required = false) String vendorIdsStr)
    {
        startPage();

        // 如果参数为空，返回空列表
        if (year == null || quarter == null || vendorIdsStr == null || vendorIdsStr.isEmpty()) {
            return getDataTable(new ArrayList<QuarterlyReport>());
        }

        // 解析厂商ID列表
        List<Long> vendorIds = Arrays.stream(vendorIdsStr.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());

        List<QuarterlyReport> list = quarterlyReportService.generateQuarterlyReport(year, quarter, vendorIds);
        return getDataTable(list);
    }

    /**
     * 导出季度归总报表
     */
    @RequiresPermissions("system:quarterly:report:export")
    @Log(title = "导出季度归总报表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(@RequestParam(value = "year", required = true) Integer year,
                            @RequestParam(value = "quarter", required = true) Integer quarter,
                            @RequestParam(value = "vendorIds", required = true) String vendorIdsStr)
    {
        if (year == null || quarter == null || vendorIdsStr == null || vendorIdsStr.isEmpty()) {
            return AjaxResult.error("请选择年份、季度和厂商");
        }

        try {
            // 解析厂商ID列表
            List<Long> vendorIds = Arrays.stream(vendorIdsStr.split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());

            List<QuarterlyReport> list = quarterlyReportService.generateQuarterlyReport(year, quarter, vendorIds);
            ExcelUtil<QuarterlyReport> util = new ExcelUtil<QuarterlyReport>(QuarterlyReport.class);
            return util.exportExcel(list, year + "年第" + quarter + "季度归总报表");
        } catch (Exception e) {
            logger.error("导出季度归总报表失败", e);
            return AjaxResult.error("导出失败：" + e.getMessage());
        }
    }

    /**
     * 导出并下载季度归总报表
     */
    @RequiresPermissions("system:quarterly:report:export")
    @Log(title = "导出并下载季度归总报表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportAndDownload")
    @ResponseBody
    public AjaxResult exportAndDownload(@RequestParam(value = "year", required = true) Integer year,
                                      @RequestParam(value = "quarter", required = true) Integer quarter,
                                      @RequestParam(value = "vendorIds", required = true) String vendorIdsStr)
    {
        if (year == null || quarter == null || vendorIdsStr == null || vendorIdsStr.isEmpty()) {
            return AjaxResult.error("请选择年份、季度和厂商");
        }

        try {
            // 解析厂商ID列表
            List<Long> vendorIds = Arrays.stream(vendorIdsStr.split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());

            // 导出季度归总报表
            String filePath = quarterlyReportService.exportQuarterlyReport(year, quarter, vendorIds);

            return AjaxResult.success("导出成功，文件路径：" + filePath);
        } catch (Exception e) {
            logger.error("导出季度归总报表失败", e);
            return AjaxResult.error("导出失败：" + e.getMessage());
        }
    }
}
