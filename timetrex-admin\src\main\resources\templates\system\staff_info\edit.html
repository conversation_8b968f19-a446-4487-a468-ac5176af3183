<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改人员信息')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-staff_info-edit" th:object="${staffInfo}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label is-required">工号：</label>
                    <div class="col-sm-8">
                        <input name="staffNo" th:field="*{staffNo}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label is-required">姓名：</label>
                    <div class="col-sm-8">
                        <input name="name" th:field="*{name}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label is-required">所属厂商：</label>
                    <div class="col-sm-8">
                        <select name="vendorId" class="form-control" th:with="type=${@dict.getType('sys_outsource_factory')}" required>
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{vendorId}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">所属项目：</label>
                    <div class="col-sm-8">
                        <select name="projectId" class="form-control" th:with="type=${@dict.getType('sys_outsource_project')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{projectId}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">角色：</label>
                    <div class="col-sm-8">
                        <select name="roleId" class="form-control" th:with="type=${@dict.getType('sys_outsource_role')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{roleId}"></option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">工作经验年限：</label>
                    <div class="col-sm-8">
                        <input name="experienceYears" th:field="*{experienceYears}" class="form-control" type="text">
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label is-required">单价(元/人月)：</label>
                    <div class="col-sm-8">
                        <input name="unitPrice" th:field="*{unitPrice}" class="form-control" type="text" required>
                    </div>
                </div>
            </div>
            <div class="col-xs-6">
                <div class="form-group">
                    <label class="col-sm-4 control-label">人员级别：</label>
                    <div class="col-sm-8">
                        <select name="staffLevel" class="form-control" th:with="type=${@dict.getType('sys_outsource_level')}">
                            <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{staffLevel}"></option>
                        </select>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "system/staff_info";
        $("#form-staff_info-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-staff_info-edit').serialize());
            }
        }
    </script>
</body>
</html>