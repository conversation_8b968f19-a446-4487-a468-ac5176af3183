package com.timetrex.system.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 企业微信配置类
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "wechat")
public class WechatConfig {
    
    /** 企业ID */
    private String corpid;
    
    /** 打卡应用配置 */
    private Checkin checkin;
    
    /** 同步配置 */
    private Sync sync;
    
    public String getCorpid() {
        return corpid;
    }
    
    public void setCorpid(String corpid) {
        this.corpid = corpid;
    }
    
    public Checkin getCheckin() {
        return checkin;
    }
    
    public void setCheckin(Checkin checkin) {
        this.checkin = checkin;
    }
    
    public Sync getSync() {
        return sync;
    }
    
    public void setSync(Sync sync) {
        this.sync = sync;
    }
    
    /**
     * 打卡应用配置
     */
    public static class Checkin {
        /** 应用Secret */
        private String secret;
        
        public String getSecret() {
            return secret;
        }
        
        public void setSecret(String secret) {
            this.secret = secret;
        }
    }
    
    /**
     * 同步配置
     */
    public static class Sync {
        /** 需要同步的用户ID列表 */
        private List<String> userIds;
        
        /** 批量处理大小（每批处理的用户数量） */
        private int batchSize = 10;
        
        /** 数据库批量插入大小 */
        private int dbBatchSize = 500;
        
        /** API请求间隔（毫秒） */
        private long requestInterval = 1000;
        
        public List<String> getUserIds() {
            return userIds;
        }
        
        public void setUserIds(List<String> userIds) {
            this.userIds = userIds;
        }
        
        public int getBatchSize() {
            return batchSize;
        }
        
        public void setBatchSize(int batchSize) {
            this.batchSize = batchSize;
        }
        
        public int getDbBatchSize() {
            return dbBatchSize;
        }
        
        public void setDbBatchSize(int dbBatchSize) {
            this.dbBatchSize = dbBatchSize;
        }
        
        public long getRequestInterval() {
            return requestInterval;
        }
        
        public void setRequestInterval(long requestInterval) {
            this.requestInterval = requestInterval;
        }
    }
}
