# 企业微信打卡数据同步配置示例
# 请根据实际情况修改配置值

# 企业微信配置
wechat:
  # 企业ID（必填）
  corpid: ww193cf34f5ed70135
  
  # 打卡应用的Secret（必填）
  checkin:
    secret: OL44F7Ik4QhMrqfPOG6rxoxIJNFaPX_OKpNypcAaGq0
  
  # 同步配置
  sync:
    # 需要同步的用户ID列表（企业微信用户ID）
    # 如果为空或不配置，将获取所有用户的打卡数据
    userIds:
      - zhangsan      # 张三的企微用户ID
      - lisi          # 李四的企微用户ID
      - wangwu        # 王五的企微用户ID
      - zhaoliu       # 赵六的企微用户ID
      - qianqi        # 钱七的企微用户ID
      # 可以继续添加更多用户ID...
      
    # 批量处理大小（每批处理的用户数量）
    # 建议值：5-20，根据用户数量和网络情况调整
    batchSize: 10
    
    # 数据库批量插入大小
    # 建议值：200-1000，根据数据库性能调整
    # 40人一个月约1200条概况数据 + 2400条详情数据
    dbBatchSize: 500
    
    # API请求间隔（毫秒）
    # 避免频繁调用企微API，建议不少于1000ms
    requestInterval: 1000

# 性能优化说明：
# 1. 对于40人的团队，一个月的数据量大约：
#    - 概况数据：40人 × 30天 = 1200条
#    - 详情数据：40人 × 30天 × 2次打卡 = 2400条
#    - 总计：约3600条数据
#
# 2. 使用dbBatchSize=500的配置：
#    - 概况数据：分3批插入（500+500+200）
#    - 详情数据：分5批插入（500×4+400）
#    - 总计：8批次，每批间隔100ms
#
# 3. 预估处理时间：
#    - API调用：约10-30秒（取决于网络和企微服务器响应）
#    - 数据转换：约1-3秒
#    - 数据库插入：约2-5秒（8批次）
#    - 总计：约15-40秒

# 用户ID获取方法：
# 1. 通过企业微信管理后台查看
# 2. 调用企微API获取部门用户列表
# 3. 从现有系统的用户表中查询对应的企微用户ID

# 注意事项：
# 1. corpid和secret需要从企业微信管理后台获取
# 2. 确保打卡应用有读取打卡数据的权限
# 3. 用户ID必须是企业微信中的真实用户ID
# 4. 建议在测试环境先验证配置的正确性
# 5. 生产环境部署前请备份数据库
