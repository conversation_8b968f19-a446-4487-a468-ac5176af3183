package com.timetrex.system.service.impl;

import java.io.File;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import com.timetrex.system.service.IEmailService;

/**
 * 邮件服务实现类
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Service
public class EmailServiceImpl implements IEmailService
{
    private static final Logger log = LoggerFactory.getLogger(EmailServiceImpl.class);

    @Autowired
    private JavaMailSender mailSender;

    @Value("${spring.mail.username}")
    private String from;

    /**
     * 发送简单文本邮件
     *
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     * @return 是否发送成功
     */
    @Override
    public boolean sendSimpleMail(String to, String subject, String content)
    {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(from);
            message.setTo(to);
            message.setSubject(subject);
            message.setText(content);
            mailSender.send(message);
            log.info("简单邮件已发送。");
            return true;
        } catch (Exception e) {
            log.error("发送简单邮件时发生异常！", e);
            return false;
        }
    }

    /**
     * 发送HTML邮件
     *
     * @param to 收件人
     * @param subject 主题
     * @param content HTML内容
     * @return 是否发送成功
     */
    @Override
    public boolean sendHtmlMail(String to, String subject, String content)
    {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true);
            mailSender.send(message);
            log.info("HTML邮件已发送。");
            return true;
        } catch (MessagingException e) {
            log.error("发送HTML邮件时发生异常！", e);
            return false;
        }
    }

    /**
     * 发送带附件的邮件
     *
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     * @param file 附件
     * @return 是否发送成功
     */
    @Override
    public boolean sendAttachmentMail(String to, String subject, String content, File file)
    {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true);

            FileSystemResource resource = new FileSystemResource(file);
            helper.addAttachment(file.getName(), resource);

            mailSender.send(message);
            log.info("带附件的邮件已发送。");
            return true;
        } catch (MessagingException e) {
            log.error("发送带附件的邮件时发生异常！", e);
            return false;
        }
    }

    /**
     * 发送带附件的邮件
     *
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     * @param filePath 附件路径
     * @return 是否发送成功
     */
    @Override
    public boolean sendAttachmentMail(String to, String subject, String content, String filePath)
    {
        File file = new File(filePath);
        return sendAttachmentMail(to, subject, content, file);
    }
}
