package com.timetrex.system.domain;

import java.math.BigDecimal;
import java.time.LocalDate;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.timetrex.common.annotation.Excel;
import com.timetrex.common.core.domain.BaseEntity;

/**
 * 核算结果对象 settlement_result
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
public class SettlementResult extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 人员姓名 */
    @Excel(name = "人员姓名")
    private String staffName;

    /** 厂商ID */
    private Long vendorId;

    /** 厂商名称 */
    @Excel(name = "厂商名称")
    private String vendorName;

    /** 核算月份 */
    @JsonFormat(pattern = "yyyy-MM")
    @Excel(name = "核算月份", width = 30, dateFormat = "yyyy-MM")
    private LocalDate month;

    /** 标准工作时长(小时) */
    @Excel(name = "标准工作时长(小时)")
    private BigDecimal standardHours;

    /** 实际打卡按9小时算 */
    @Excel(name = "实际打卡按9小时算")
    private BigDecimal calculatedHours;

    /** 人月工时 */
    @Excel(name = "人月工时")
    private BigDecimal workDays;

    /** 结算金额 */
    @Excel(name = "结算金额")
    private BigDecimal amount;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }


    public String getStaffName()
    {
        return staffName;
    }

    public void setStaffName(String staffName)
    {
        this.staffName = staffName;
    }

    public Long getVendorId()
    {
        return vendorId;
    }

    public void setVendorId(Long vendorId)
    {
        this.vendorId = vendorId;
    }

    public String getVendorName()
    {
        return vendorName;
    }

    public void setVendorName(String vendorName)
    {
        this.vendorName = vendorName;
    }

    public void setMonth(LocalDate month)
    {
        this.month = month;
    }

    public LocalDate getMonth()
    {
        return month;
    }
    public void setStandardHours(BigDecimal standardHours)
    {
        this.standardHours = standardHours;
    }

    public BigDecimal getStandardHours()
    {
        return standardHours;
    }
    public void setCalculatedHours(BigDecimal calculatedHours)
    {
        this.calculatedHours = calculatedHours;
    }

    public BigDecimal getCalculatedHours()
    {
        return calculatedHours;
    }
    public void setWorkDays(BigDecimal workDays)
    {
        this.workDays = workDays;
    }

    public BigDecimal getWorkDays()
    {
        return workDays;
    }
    public void setAmount(BigDecimal amount)
    {
        this.amount = amount;
    }

    public BigDecimal getAmount()
    {
        return amount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("staffName", getStaffName())
            .append("vendorId", getVendorId())
            .append("vendorName", getVendorName())
            .append("month", getMonth())
            .append("standardHours", getStandardHours())
            .append("calculatedHours", getCalculatedHours())
            .append("workDays", getWorkDays())
            .append("amount", getAmount())
            .toString();
    }
}
