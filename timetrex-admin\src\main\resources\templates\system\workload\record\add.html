<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增人员工作量登记')" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-workload-add">
            <div class="form-group">
                <label class="col-sm-3 control-label">厂商：</label>
                <div class="col-sm-8">
                    <input name="vendorName" id="vendorName" class="form-control" type="text" readonly placeholder="根据员工工号自动获取">
                    <input name="vendorId" id="vendorId" type="hidden">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">员工工号：</label>
                <div class="col-sm-8">
                    <input name="staffNo" id="staffNo" class="form-control" type="text">
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 输入工号后自动获取员工信息，或者输入姓名自动获取工号</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">员工姓名：</label>
                <div class="col-sm-8">
                    <input name="staffName" id="staffName" class="form-control" type="text" required>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 输入姓名后自动获取工号和厂商信息</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">登记年份：</label>
                <div class="col-sm-8">
                    <select name="recordYear" class="form-control" required>
                        <option value="">请选择年份</option>
                        <option th:each="year : ${#numbers.sequence(2024, 2026)}"
                                th:value="${year}" th:text="${year}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">登记月份：</label>
                <div class="col-sm-8">
                    <select name="recordMonth" class="form-control" required>
                        <option value="">请选择月份</option>
                        <option th:each="month : ${#numbers.sequence(1, 12)}"
                                th:value="${month}" th:text="${month + '月'}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">工作小时数：</label>
                <div class="col-sm-8">
                    <input name="workHours" id="workHours" class="form-control" type="number" step="0.01" min="0" required>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 输入工作小时数，系统自动计算工作天数（÷9小时/天）</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">工作天数：</label>
                <div class="col-sm-8">
                    <input name="workDays" id="workDays" class="form-control" type="number" step="0.01" min="0" max="31" readonly>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 根据工作小时数自动计算（工作小时数 ÷ 9）</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">人月数：</label>
                <div class="col-sm-8">
                    <input id="personMonths" class="form-control" type="text" readonly>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 根据工作天数和厂商平均计薪工作日自动计算</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">工作内容描述：</label>
                <div class="col-sm-8">
                    <textarea name="workDescription" class="form-control" rows="4" required placeholder="请详细描述本月的主要工作内容..."></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remark" class="form-control" rows="2" placeholder="其他需要说明的信息..."></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-select-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/workload/record";
        $("#form-workload-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix, $('#form-workload-add').serialize());
            }
        }

        $(function() {
            // 初始化下拉框
            $('.selectpicker').selectpicker();

            // 设置默认年份为当前年份
            var currentYear = new Date().getFullYear();
            $('select[name="recordYear"]').val(currentYear);

            // 设置默认月份为当前月份
            var currentMonth = new Date().getMonth() + 1;
            $('select[name="recordMonth"]').val(currentMonth);

            // 员工工号输入事件
            $("#staffNo").on('blur', function() {
                var staffNo = $(this).val().trim();
                if (staffNo) {
                    getStaffInfoByNo(staffNo);
                }
            });

            // 员工姓名输入事件
            $("#staffName").on('blur', function() {
                var staffName = $(this).val().trim();
                if (staffName) {
                    getStaffInfoByName(staffName);
                }
            });

            // 工作小时数输入事件
            $("#workHours").on('input', function() {
                calculateWorkDays();
            });

            // 厂商变化时重新计算人月数
            $("#vendorId").on('change', function() {
                calculatePersonMonths();
            });
        });

        // 根据工号获取员工信息
        function getStaffInfoByNo(staffNo) {
            console.log("根据工号获取员工信息：", staffNo);
            $.ajax({
                url: ctx + "system/staff_info/getByStaffNo",
                type: "GET",
                data: { staffNo: staffNo },
                success: function(result) {
                    console.log("获取员工信息结果：", result);
                    if (result.code == 0 && result.data) {
                        var staff = result.data;
                        $("#staffName").val(staff.name);
                        $("#vendorId").val(staff.vendorId);

                        // 获取厂商信息
                        if (staff.vendorId) {
                            console.log("获取厂商信息，厂商ID：", staff.vendorId);
                            getVendorInfo(staff.vendorId);
                        } else {
                            console.log("员工没有关联厂商");
                        }
                    } else {
                        console.log("未找到员工信息或返回错误：", result.msg);
                        $.modal.alertWarning(result.msg || "未找到该工号的员工信息");
                        clearStaffInfo();
                    }
                },
                error: function(xhr, status, error) {
                    console.error("获取员工信息异常：", error);
                    $.modal.alertError("获取员工信息失败");
                }
            });
        }

        // 根据姓名获取员工信息
        function getStaffInfoByName(staffName) {
            console.log("根据姓名获取员工信息：", staffName);
            $.ajax({
                url: ctx + "system/staff_info/getByStaffName",
                type: "GET",
                data: { staffName: staffName },
                success: function(result) {
                    console.log("根据姓名获取员工信息结果：", result);
                    if (result.code == 0 && result.data) {
                        var staff = result.data;
                        $("#staffNo").val(staff.staffNo);
                        $("#vendorId").val(staff.vendorId);

                        // 获取厂商信息
                        if (staff.vendorId) {
                            console.log("获取厂商信息，厂商ID：", staff.vendorId);
                            getVendorInfo(staff.vendorId);
                        } else {
                            console.log("员工没有关联厂商");
                        }
                    } else {
                        console.log("未找到员工信息或返回错误：", result.msg);
                        $.modal.alertWarning(result.msg || "未找到该姓名的员工信息");
                        clearStaffInfo();
                    }
                },
                error: function(xhr, status, error) {
                    console.error("根据姓名获取员工信息异常：", error);
                    $.modal.alertError("获取员工信息失败");
                }
            });
        }

        // 根据厂商ID获取厂商信息
        function getVendorInfo(vendorId) {
            if (!vendorId) {
                console.log("厂商ID为空，跳过获取厂商信息");
                return;
            }

            $.ajax({
                url: ctx + "system/vendor/getById",
                type: "GET",
                data: { id: vendorId },
                success: function(result) {
                    if (result.code == 0 && result.data) {
                        $("#vendorName").val(result.data.name);
                        // 重新计算人月数
                        calculatePersonMonths();
                    } else {
                        console.log("获取厂商信息失败：", result.msg);
                        $("#vendorName").val("");
                    }
                },
                error: function(xhr, status, error) {
                    console.error("获取厂商信息异常：", error);
                    $.modal.alertError("获取厂商信息失败");
                }
            });
        }

        // 清空员工信息
        function clearStaffInfo() {
            $("#staffNo").val("");
            $("#staffName").val("");
            $("#vendorId").val("");
            $("#vendorName").val("");
            $("#personMonths").val("");
        }

        // 计算工作天数
        function calculateWorkDays() {
            var workHours = parseFloat($("#workHours").val()) || 0;
            var workDays = workHours / 9; // 每天9小时
            $("#workDays").val(workDays.toFixed(2));

            // 重新计算人月数
            calculatePersonMonths();
        }

        // 计算人月数
        function calculatePersonMonths() {
            var workDays = parseFloat($("#workDays").val()) || 0;
            var vendorId = $("#vendorId").val();

            if (workDays > 0 && vendorId) {
                $.ajax({
                    url: ctx + "system/vendor/getAvgWorkDays",
                    type: "GET",
                    data: { vendorId: vendorId },
                    success: function(result) {
                        if (result.code == 0) {
                            var avgWorkDays = parseFloat(result.data) || 22;
                            var personMonths = workDays / avgWorkDays;
                            $("#personMonths").val(personMonths.toFixed(4) + " 人月");
                        }
                    }
                });
            } else {
                $("#personMonths").val("");
            }
        }
    </script>
</body>
</html>
