<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增人员工作量登记')" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-workload-add">
            <div class="form-group">
                <label class="col-sm-3 control-label">厂商：</label>
                <div class="col-sm-8">
                    <input name="vendorName" id="vendorName" class="form-control" type="text" readonly placeholder="根据员工工号自动获取">
                    <input name="vendorId" id="vendorId" type="hidden">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">员工工号：</label>
                <div class="col-sm-8">
                    <input name="staffNo" id="staffNo" class="form-control" type="text" required>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 输入工号后自动获取员工信息和厂商信息</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">员工姓名：</label>
                <div class="col-sm-8">
                    <input name="staffName" id="staffName" class="form-control" type="text" readonly placeholder="根据员工工号自动获取">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">登记年份：</label>
                <div class="col-sm-8">
                    <select name="recordYear" class="form-control" required>
                        <option value="">请选择年份</option>
                        <option th:each="year : ${#numbers.sequence(2024, 2026)}"
                                th:value="${year}" th:text="${year}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">登记月份：</label>
                <div class="col-sm-8">
                    <select name="recordMonth" class="form-control" required>
                        <option value="">请选择月份</option>
                        <option th:each="month : ${#numbers.sequence(1, 12)}"
                                th:value="${month}" th:text="${month + '月'}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">工作天数：</label>
                <div class="col-sm-8">
                    <input name="workDays" class="form-control" type="number" step="0.01" min="0" max="31" required>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请输入实际工作天数，支持小数</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">工作小时数：</label>
                <div class="col-sm-8">
                    <input name="workHours" class="form-control" type="number" step="0.01" min="0">
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 可选，用于记录总工作小时数</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">工作内容描述：</label>
                <div class="col-sm-8">
                    <textarea name="workDescription" class="form-control" rows="4" required placeholder="请详细描述本月的主要工作内容..."></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remark" class="form-control" rows="2" placeholder="其他需要说明的信息..."></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-select-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/workload/record";
        $("#form-workload-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix, $('#form-workload-add').serialize());
            }
        }

        $(function() {
            // 初始化下拉框
            $('.selectpicker').selectpicker();

            // 设置默认年份为当前年份
            var currentYear = new Date().getFullYear();
            $('select[name="recordYear"]').val(currentYear);

            // 设置默认月份为当前月份
            var currentMonth = new Date().getMonth() + 1;
            $('select[name="recordMonth"]').val(currentMonth);

            // 员工工号输入事件
            $("#staffNo").on('blur', function() {
                var staffNo = $(this).val().trim();
                if (staffNo) {
                    getStaffInfo(staffNo);
                }
            });
        });

        // 根据工号获取员工信息
        function getStaffInfo(staffNo) {
            $.ajax({
                url: ctx + "system/staff_info/getByStaffNo",
                type: "GET",
                data: { staffNo: staffNo },
                success: function(result) {
                    if (result.code == 0 && result.data) {
                        var staff = result.data;
                        $("#staffName").val(staff.name);
                        $("#vendorId").val(staff.vendorId);

                        // 获取厂商信息
                        if (staff.vendorId) {
                            getVendorInfo(staff.vendorId);
                        }
                    } else {
                        $.modal.alertWarning("未找到该工号的员工信息");
                        $("#staffName").val("");
                        $("#vendorId").val("");
                        $("#vendorName").val("");
                    }
                },
                error: function() {
                    $.modal.alertError("获取员工信息失败");
                }
            });
        }

        // 根据厂商ID获取厂商信息
        function getVendorInfo(vendorId) {
            $.ajax({
                url: ctx + "system/vendor/getById",
                type: "GET",
                data: { id: vendorId },
                success: function(result) {
                    if (result.code == 0 && result.data) {
                        $("#vendorName").val(result.data.name);
                    }
                }
            });
        }
    </script>
</body>
</html>
