<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增人员工作量登记')" />
    <th:block th:include="include :: bootstrap-select-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-workload-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">厂商：</label>
                <div class="col-sm-8">
                    <select name="vendorId" class="form-control selectpicker" data-live-search="true" required>
                        <option value="">请选择厂商</option>
                        <option th:each="vendor : ${vendorList}" th:value="${vendor.id}" th:text="${vendor.name}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">员工工号：</label>
                <div class="col-sm-8">
                    <input name="staffNo" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">员工姓名：</label>
                <div class="col-sm-8">
                    <input name="staffName" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">登记年份：</label>
                <div class="col-sm-8">
                    <select name="recordYear" class="form-control" required>
                        <option value="">请选择年份</option>
                        <option th:each="year : ${#numbers.sequence(2024, 2026)}" 
                                th:value="${year}" th:text="${year}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">登记月份：</label>
                <div class="col-sm-8">
                    <select name="recordMonth" class="form-control" required>
                        <option value="">请选择月份</option>
                        <option th:each="month : ${#numbers.sequence(1, 12)}" 
                                th:value="${month}" th:text="${month + '月'}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">工作天数：</label>
                <div class="col-sm-8">
                    <input name="workDays" class="form-control" type="number" step="0.01" min="0" max="31" required>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 请输入实际工作天数，支持小数</span>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">工作小时数：</label>
                <div class="col-sm-8">
                    <input name="workHours" class="form-control" type="number" step="0.01" min="0">
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 可选，用于记录总工作小时数</span>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">工作内容描述：</label>
                <div class="col-sm-8">
                    <textarea name="workDescription" class="form-control" rows="4" required placeholder="请详细描述本月的主要工作内容..."></textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remark" class="form-control" rows="2" placeholder="其他需要说明的信息..."></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-select-js" />
    <script th:inline="javascript">
        var prefix = ctx + "system/workload/record";
        $("#form-workload-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix, $('#form-workload-add').serialize());
            }
        }

        $(function() {
            // 初始化下拉框
            $('.selectpicker').selectpicker();
            
            // 设置默认年份为当前年份
            var currentYear = new Date().getFullYear();
            $('select[name="recordYear"]').val(currentYear);
            
            // 设置默认月份为当前月份
            var currentMonth = new Date().getMonth() + 1;
            $('select[name="recordMonth"]').val(currentMonth);
        });
    </script>
</body>
</html>
