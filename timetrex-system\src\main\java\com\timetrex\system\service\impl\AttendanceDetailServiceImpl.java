package com.timetrex.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.timetrex.system.mapper.AttendanceDetailMapper;
import com.timetrex.system.domain.AttendanceDetail;
import com.timetrex.system.service.IAttendanceDetailService;
import com.timetrex.common.core.text.Convert;

/**
 * 打卡详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
@Service
public class AttendanceDetailServiceImpl implements IAttendanceDetailService
{
    @Autowired
    private AttendanceDetailMapper attendanceDetailMapper;

    /**
     * 查询打卡详情
     *
     * @param id 打卡详情主键
     * @return 打卡详情
     */
    @Override
    public AttendanceDetail selectAttendanceDetailById(Long id)
    {
        return attendanceDetailMapper.selectAttendanceDetailById(id);
    }

    /**
     * 查询打卡详情列表
     *
     * @param attendanceDetail 打卡详情
     * @return 打卡详情
     */
    @Override
    public List<AttendanceDetail> selectAttendanceDetailList(AttendanceDetail attendanceDetail)
    {
        return attendanceDetailMapper.selectAttendanceDetailList(attendanceDetail);
    }

    /**
     * 新增打卡详情
     *
     * @param attendanceDetail 打卡详情
     * @return 结果
     */
    @Override
    public int insertAttendanceDetail(AttendanceDetail attendanceDetail)
    {
        return attendanceDetailMapper.insertAttendanceDetail(attendanceDetail);
    }

    /**
     * 批量新增打卡详情
     *
     * @param attendanceDetailList 打卡详情列表
     * @return 结果
     */
    @Override
    public int batchInsertAttendanceDetail(List<AttendanceDetail> attendanceDetailList)
    {
        return attendanceDetailMapper.batchInsertAttendanceDetail(attendanceDetailList);
    }

    /**
     * 修改打卡详情
     *
     * @param attendanceDetail 打卡详情
     * @return 结果
     */
    @Override
    public int updateAttendanceDetail(AttendanceDetail attendanceDetail)
    {
        return attendanceDetailMapper.updateAttendanceDetail(attendanceDetail);
    }

    /**
     * 批量删除打卡详情
     *
     * @param ids 需要删除的打卡详情主键
     * @return 结果
     */
    @Override
    public int deleteAttendanceDetailByIds(String ids)
    {
        return attendanceDetailMapper.deleteAttendanceDetailByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除打卡详情信息
     *
     * @param id 打卡详情主键
     * @return 结果
     */
    @Override
    public int deleteAttendanceDetailById(Long id)
    {
        return attendanceDetailMapper.deleteAttendanceDetailById(id);
    }

    /**
     * 检查设备ID是否被多人使用
     *
     * @param deviceId 设备ID
     * @return 是否被多人使用
     */
    @Override
    public boolean checkDeviceUsedByMultipleStaff(String deviceId)
    {
        return attendanceDetailMapper.checkDeviceUsedByMultipleStaff(deviceId) > 1;
    }

    /**
     * 批量保存打卡详情
     *
     * @param attendanceDetailList 打卡详情列表
     * @return 结果
     */
    @Override
    public int batchSave(List<AttendanceDetail> attendanceDetailList)
    {
        if (attendanceDetailList == null || attendanceDetailList.isEmpty()) {
            return 0;
        }
        return batchInsertAttendanceDetail(attendanceDetailList);
    }
}
