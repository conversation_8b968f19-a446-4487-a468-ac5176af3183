# 数据转换测试示例

## 测试目的
验证企微API返回数据的正确解析和转换。

## 1. 打卡日报数据测试

### 输入数据（企微API返回）
```json
{
    "errcode": 0,
    "errmsg": "ok",
    "datas": [
        {
            "base_info": {
                "date": 1599062400,
                "record_type": 1,
                "name": "张三",
                "name_ex": "<PERSON> Zhang",
                "departs_name": "技术部/开发组",
                "acctid": "zhangsan",
                "rule_info": {
                    "groupid": 10,
                    "groupname": "标准工作制",
                    "scheduleid": 1,
                    "schedulename": "早九晚六"
                },
                "day_type": 0
            },
            "summary_info": {
                "checkin_count": 2,
                "regular_work_sec": 28800,
                "standard_work_sec": 28800,
                "earliest_time": 1599094800,
                "lastest_time": 1599123600
            }
        }
    ]
}
```

### 期望输出（系统数据）
```java
AttendanceSummary {
    staffNo: "zhang<PERSON>",
    name: "张三",
    department: "技术部/开发组",
    position: "员工",
    recordDate: LocalDate.of(2020, 9, 3),
    dayOfWeek: "星期四",
    workRule: "标准工作制",
    shift: "早九晚六",
    checkCount: 2,
    standardHours: BigDecimal.valueOf(8.00),
    actualHours: BigDecimal.valueOf(8.00),
    calculatedHours: BigDecimal.valueOf(8.00),
    earliestCheck: LocalTime.of(9, 0, 0),
    latestCheck: LocalTime.of(17, 0, 0)
}
```

### 关键转换逻辑
1. **时间戳转换**：
   - `date: 1599062400` → `2020-09-03`
   - `earliest_time: 1599094800` → `09:00:00`
   - `lastest_time: 1599123600` → `17:00:00`

2. **工作时长转换**：
   - `regular_work_sec: 28800` → `8.00小时` (28800/3600)
   - `standard_work_sec: 28800` → `8.00小时`

3. **字段映射**：
   - `acctid` → `staffNo`
   - `departs_name` → `department`
   - `groupname` → `workRule`

## 2. 打卡详情数据测试

### 输入数据（企微API返回）
```json
{
    "errcode": 0,
    "errmsg": "ok",
    "checkindata": [
        {
            "userid": "zhangsan",
            "groupname": "标准工作制",
            "checkin_type": "上班打卡",
            "exception_type": "",
            "checkin_time": 1599094800,
            "location_title": "公司大厦",
            "location_detail": "北京市朝阳区xxx大厦",
            "notes": "",
            "deviceid": "iPhone12",
            "sch_checkin_time": 1599094800
        },
        {
            "userid": "zhangsan",
            "groupname": "标准工作制",
            "checkin_type": "下班打卡",
            "exception_type": "时间异常",
            "checkin_time": 1599125400,
            "location_title": "公司大厦",
            "notes": "加班",
            "deviceid": "iPhone12",
            "sch_checkin_time": 1599123600
        }
    ]
}
```

### 期望输出（系统数据）
```java
// 上班打卡记录
AttendanceDetail {
    staffNo: "zhangsan",
    name: "zhangsan",
    department: "未知部门",
    position: "员工",
    checkDate: LocalDate.of(2020, 9, 3),
    dayOfWeek: "星期四",
    actualTime: LocalTime.of(9, 0, 0),
    checkType: "上班",
    checkStatus: "正常",
    location: "公司大厦",
    deviceId: "iPhone12",
    remark: "",
    workRule: "标准工作制",
    scheduledTime: LocalTime.of(9, 0, 0)
}

// 下班打卡记录
AttendanceDetail {
    staffNo: "zhangsan",
    name: "zhangsan",
    department: "未知部门",
    position: "员工",
    checkDate: LocalDate.of(2020, 9, 3),
    dayOfWeek: "星期四",
    actualTime: LocalTime.of(17, 30, 0),
    checkType: "下班",
    checkStatus: "时间异常",
    location: "公司大厦",
    deviceId: "iPhone12",
    remark: "加班",
    workRule: "标准工作制",
    scheduledTime: LocalTime.of(17, 0, 0)
}
```

### 关键转换逻辑
1. **打卡类型判断**：
   - `"上班打卡"` → `"上班"`
   - `"下班打卡"` → `"下班"`

2. **状态判断**：
   - `exception_type: ""` → `"正常"`
   - `exception_type: "时间异常"` → `"时间异常"`

3. **时间转换**：
   - `checkin_time: 1599094800` → `09:00:00`
   - `sch_checkin_time: 1599094800` → `09:00:00`

## 3. 边界情况测试

### 缺失字段处理
```json
{
    "base_info": {
        "date": 1599062400,
        "name": "李四",
        "acctid": "lisi",
        "rule_info": null
    },
    "summary_info": {
        "checkin_count": 1
    }
}
```

期望处理：
- `workRule`: "默认规则"（rule_info为null时）
- `shift`: null或空字符串
- `standardHours`: null（字段不存在时）

### 异常数据处理
```json
{
    "checkindata": [
        {
            "userid": "wangwu",
            "checkin_type": "外出打卡",
            "exception_type": "地点异常;设备异常",
            "checkin_time": null
        }
    ]
}
```

期望处理：
- `checkType`: "外出"
- `checkStatus`: "地点异常;设备异常"
- `actualTime`: null（时间戳为null时）
- 记录错误日志但不中断处理

## 4. 性能测试

### 大数据量测试
- **数据量**：40人×30天×2次打卡 = 2400条详情数据
- **处理时间**：预期20-45秒
- **内存使用**：每批500条，峰值内存可控
- **成功率**：预期>99%（允许少量数据转换失败）

### 并发测试
- **批次处理**：8批次并行处理
- **数据库连接**：控制连接池大小
- **错误恢复**：单批失败不影响其他批次

## 5. 验证方法

### 单元测试
```java
@Test
public void testConvertToSummaryList() {
    // 准备测试数据
    List<Map<String, Object>> dayDataList = createTestDayData();
    
    // 执行转换
    List<AttendanceSummary> result = service.convertToSummaryList(dayDataList);
    
    // 验证结果
    assertEquals(1, result.size());
    assertEquals("zhangsan", result.get(0).getStaffNo());
    assertEquals("张三", result.get(0).getName());
    // ... 更多断言
}
```

### 集成测试
```java
@Test
public void testSyncMonthlyAttendance() {
    // 模拟企微API返回
    mockWechatApi();
    
    // 执行同步
    Map<String, Object> result = service.syncMonthlyAttendance(2020, 9);
    
    // 验证结果
    assertTrue((Boolean) result.get("success"));
    assertTrue((Integer) result.get("summaryCount") > 0);
}
```

## 6. 监控指标

### 数据质量指标
- 转换成功率
- 字段完整性
- 时间格式正确性
- 数据一致性

### 性能指标
- 处理时间
- 内存使用
- 数据库连接数
- API调用次数

通过这些测试用例，可以确保数据转换的正确性和系统的稳定性。
