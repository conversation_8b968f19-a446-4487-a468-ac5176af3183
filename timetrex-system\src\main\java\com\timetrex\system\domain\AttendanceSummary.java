package com.timetrex.system.domain;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.timetrex.common.annotation.Excel;
import com.timetrex.common.core.domain.BaseEntity;

/**
 * 考勤概况统计与打卡明细对象 attendance_summary
 *
 * <AUTHOR>
 * @date 2025-05-09
 */
public class AttendanceSummary extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 账号 */
    @Excel(name = "账号")
    private String staffNo;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "时间", width = 30, dateFormat = "yyyy-MM-dd")
    private LocalDate recordDate;

    /** 部门 */
    @Excel(name = "部门")
    private String department;

    /** 职务 */
    @Excel(name = "职务")
    private String position;

    /** 所属规则 */
    @Excel(name = "所属规则")
    private String workRule;

    /** 班次 */
    @Excel(name = "班次")
    private String shift;

    /** 最早打卡时间 */
    @JsonFormat(pattern = "HH:mm:ss")
    @Excel(name = "最早打卡时间", width = 30, dateFormat = "HH:mm:ss")
    private LocalTime earliestCheck;

    /** 最晚打卡时间 */
    @JsonFormat(pattern = "HH:mm:ss")
    @Excel(name = "最晚打卡时间", width = 30, dateFormat = "HH:mm:ss")
    private LocalTime latestCheck;

    /** 打卡次数(次) */
    @Excel(name = "打卡次数(次)")
    private Integer checkCount;

    /** 标准工作时长(小时) */
    @Excel(name = "标准工作时长(小时)")
    private BigDecimal standardHours;

    /** 实际工作时长(小时) */
    @Excel(name = "实际工作时长(小时)")
    private BigDecimal actualHours;

    /** 实际打卡按9小时算 */
    @Excel(name = "实际打卡按9小时算")
    private BigDecimal calculatedHours;

    /** 星期几 */
    @Excel(name = "星期几")
    private String dayOfWeek;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setStaffNo(String staffNo)
    {
        this.staffNo = staffNo;
    }

    public String getStaffNo()
    {
        return staffNo;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setRecordDate(LocalDate recordDate)
    {
        this.recordDate = recordDate;
    }

    public LocalDate getRecordDate()
    {
        return recordDate;
    }
    public void setDepartment(String department)
    {
        this.department = department;
    }

    public String getDepartment()
    {
        return department;
    }
    public void setPosition(String position)
    {
        this.position = position;
    }

    public String getPosition()
    {
        return position;
    }
    public void setWorkRule(String workRule)
    {
        this.workRule = workRule;
    }

    public String getWorkRule()
    {
        return workRule;
    }
    public void setShift(String shift)
    {
        this.shift = shift;
    }

    public String getShift()
    {
        return shift;
    }
    public void setEarliestCheck(LocalTime earliestCheck)
    {
        this.earliestCheck = earliestCheck;
    }

    public LocalTime getEarliestCheck()
    {
        return earliestCheck;
    }
    public void setLatestCheck(LocalTime latestCheck)
    {
        this.latestCheck = latestCheck;
    }

    public LocalTime getLatestCheck()
    {
        return latestCheck;
    }
    public void setCheckCount(Integer checkCount)
    {
        this.checkCount = checkCount;
    }

    public Integer getCheckCount()
    {
        return checkCount;
    }
    public void setStandardHours(BigDecimal standardHours)
    {
        this.standardHours = standardHours;
    }

    public BigDecimal getStandardHours()
    {
        return standardHours;
    }
    public void setActualHours(BigDecimal actualHours)
    {
        this.actualHours = actualHours;
    }

    public BigDecimal getActualHours()
    {
        return actualHours;
    }
    public void setCalculatedHours(BigDecimal calculatedHours)
    {
        this.calculatedHours = calculatedHours;
    }

    public BigDecimal getCalculatedHours()
    {
        return calculatedHours;
    }

    public void setDayOfWeek(String dayOfWeek)
    {
        this.dayOfWeek = dayOfWeek;
    }

    public String getDayOfWeek()
    {
        return dayOfWeek;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("staffNo", getStaffNo())
            .append("name", getName())
            .append("recordDate", getRecordDate())
            .append("department", getDepartment())
            .append("position", getPosition())
            .append("workRule", getWorkRule())
            .append("shift", getShift())
            .append("earliestCheck", getEarliestCheck())
            .append("latestCheck", getLatestCheck())
            .append("checkCount", getCheckCount())
            .append("standardHours", getStandardHours())
            .append("actualHours", getActualHours())
            .append("calculatedHours", getCalculatedHours())
            .append("dayOfWeek", getDayOfWeek())
            .toString();
    }
}
