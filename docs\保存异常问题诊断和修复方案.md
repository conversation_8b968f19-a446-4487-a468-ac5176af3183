# 保存异常问题诊断和修复方案

## 问题分析

根据代码分析，保存异常可能的原因有：

### 1. 数据库表不存在
- `staff_workload_record` 表可能没有创建
- 表结构与实体类字段不匹配

### 2. 数据验证失败
- 必填字段为空
- 数据类型不匹配
- 唯一约束冲突

### 3. 外键约束问题
- `vendor_id` 引用的厂商不存在
- 员工信息不完整

### 4. MyBatis映射问题
- SQL语句错误
- 参数映射不正确

## 已实施的修复措施

### 1. Controller层优化

#### 数据验证增强：
```java
// 必填字段验证
if (staffWorkloadRecord.getStaffNo() == null || staffWorkloadRecord.getStaffNo().trim().isEmpty()) {
    return AjaxResult.error("员工工号不能为空");
}
if (staffWorkloadRecord.getStaffName() == null || staffWorkloadRecord.getStaffName().trim().isEmpty()) {
    return AjaxResult.error("员工姓名不能为空");
}
// ... 其他验证
```

#### 自动计算工作天数：
```java
// 计算工作天数
if (staffWorkloadRecord.getWorkHours() != null) {
    BigDecimal workDays = staffWorkloadRecord.getWorkHours().divide(new BigDecimal("9"), 2, RoundingMode.HALF_UP);
    staffWorkloadRecord.setWorkDays(workDays);
}
```

#### 异常处理：
```java
try {
    // 业务逻辑
    return toAjax(staffWorkloadRecordService.insertStaffWorkloadRecord(staffWorkloadRecord));
} catch (Exception e) {
    logger.error("保存工作量登记失败", e);
    return AjaxResult.error("保存失败：" + e.getMessage());
}
```

### 2. Service层优化

#### 自动数据填充：
```java
// 从人员信息中获取厂商信息
if (staffWorkloadRecord.getStaffNo() != null) {
    StaffInfo staffInfo = staffInfoService.selectStaffInfoByStaffNo(staffWorkloadRecord.getStaffNo());
    if (staffInfo != null) {
        // 设置厂商ID和名称
        staffWorkloadRecord.setVendorId(staffInfo.getVendorId());
        // 设置厂商名称
        if (staffInfo.getVendorId() != null) {
            Vendor vendor = vendorMapper.selectVendorById(staffInfo.getVendorId());
            if (vendor != null) {
                staffWorkloadRecord.setVendorName(vendor.getName());
            }
        }
    }
}
```

## 诊断步骤

### 1. 检查数据库表
执行以下SQL检查表是否存在：
```sql
-- 检查表是否存在
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'staff_workload_record';

-- 检查表结构
DESCRIBE staff_workload_record;
```

### 2. 检查依赖表
```sql
-- 检查staff_info表
SELECT COUNT(*) FROM staff_info;
SELECT id, staff_no, name, vendor_id, unit_price FROM staff_info LIMIT 5;

-- 检查vendor表
SELECT COUNT(*) FROM vendor;
SELECT id, name, avg_work_days FROM vendor LIMIT 5;
```

### 3. 检查应用日志
查看应用启动日志和错误日志：
- MyBatis映射文件是否正确加载
- 数据库连接是否正常
- 具体的异常堆栈信息

## 解决方案

### 方案1：创建数据库表
如果表不存在，执行以下SQL：
```sql
-- 创建staff_workload_record表
CREATE TABLE staff_workload_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    vendor_id BIGINT COMMENT '厂商ID',
    vendor_name VARCHAR(100) COMMENT '厂商名称',
    staff_no VARCHAR(50) NOT NULL COMMENT '员工工号',
    staff_name VARCHAR(100) NOT NULL COMMENT '员工姓名',
    record_year INT NOT NULL COMMENT '登记年份',
    record_month INT NOT NULL COMMENT '登记月份',
    work_description TEXT COMMENT '工作内容描述',
    work_days DECIMAL(5,2) DEFAULT 0.00 COMMENT '工作天数',
    work_hours DECIMAL(8,2) COMMENT '工作小时数',
    person_months DECIMAL(8,4) COMMENT '人月数',
    unit_price DECIMAL(10,2) COMMENT '人员单价',
    settlement_amount DECIMAL(12,2) COMMENT '结算金额',
    status VARCHAR(20) DEFAULT 'DRAFT' COMMENT '状态',
    submit_time DATETIME COMMENT '提交时间',
    calculate_time DATETIME COMMENT '计算时间',
    calculate_user VARCHAR(100) COMMENT '计算人员',
    approve_time DATETIME COMMENT '审批时间',
    approve_user VARCHAR(100) COMMENT '审批人员',
    remark TEXT COMMENT '备注',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志',
    
    UNIQUE KEY uk_vendor_staff_month (vendor_id, staff_no, record_year, record_month),
    INDEX idx_vendor_month (vendor_id, record_year, record_month),
    INDEX idx_staff (staff_no, staff_name),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='人员工作量登记表';
```

### 方案2：检查staff_info表字段
如果staff_info表缺少必要字段：
```sql
-- 添加vendor_id字段
ALTER TABLE staff_info ADD COLUMN vendor_id BIGINT COMMENT '厂商ID';

-- 添加unit_price字段
ALTER TABLE staff_info ADD COLUMN unit_price DECIMAL(10,2) COMMENT '人员单价（元/人月）';
```

### 方案3：检查vendor表字段
如果vendor表缺少avg_work_days字段：
```sql
-- 添加avg_work_days字段
ALTER TABLE vendor ADD COLUMN avg_work_days DECIMAL(5,2) DEFAULT 22.00 COMMENT '平均工作日/月';
```

### 方案4：插入测试数据
```sql
-- 插入测试厂商数据
INSERT INTO vendor (name, avg_work_days) VALUES 
('测试科技公司', 22.00),
('示例软件公司', 21.50);

-- 插入测试员工数据
INSERT INTO staff_info (staff_no, name, vendor_id, unit_price, status) VALUES 
('test001', '测试员工1', 1, 25000.00, 1),
('test002', '测试员工2', 1, 22000.00, 1),
('test003', '测试员工3', 2, 28000.00, 1);
```

## 测试验证

### 1. 基本功能测试
1. 进入工作量登记添加页面
2. 填写以下信息：
   - 员工工号：test001
   - 员工姓名：测试员工1（应自动填充）
   - 登记年份：2024
   - 登记月份：12
   - 工作小时数：180
   - 工作内容：测试工作内容
3. 点击确定保存

### 2. 数据验证测试
1. 尝试保存空数据，验证验证逻辑
2. 尝试保存重复数据，验证唯一约束
3. 验证工作天数自动计算是否正确

### 3. 查看保存结果
```sql
-- 查看保存的数据
SELECT * FROM staff_workload_record ORDER BY create_time DESC LIMIT 5;
```

## 常见问题及解决方案

### 问题1：表不存在
**现象**：Table 'database.staff_workload_record' doesn't exist
**解决**：执行建表SQL

### 问题2：字段不存在
**现象**：Unknown column 'vendor_id' in 'field list'
**解决**：检查并添加缺失字段

### 问题3：唯一约束冲突
**现象**：Duplicate entry for key 'uk_vendor_staff_month'
**解决**：检查是否已存在相同员工的同月记录

### 问题4：外键约束失败
**现象**：Cannot add or update a child row
**解决**：确保vendor_id对应的厂商存在

### 问题5：数据类型不匹配
**现象**：Data truncation or type mismatch
**解决**：检查字段长度和数据类型

## 监控和日志

### 1. 启用详细日志
在application.yml中添加：
```yaml
logging:
  level:
    com.timetrex.system.mapper: DEBUG
    org.springframework.jdbc: DEBUG
```

### 2. 查看SQL执行日志
观察MyBatis生成的SQL语句是否正确

### 3. 数据库慢查询日志
检查是否有性能问题

通过以上诊断和修复方案，应该能够解决保存异常的问题。建议按照步骤逐一检查和修复。
