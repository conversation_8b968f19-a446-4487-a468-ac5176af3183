获取打卡日报数据

​	返回参数：

| 参数                                               | 类型     | 说明                                                         |
| -------------------------------------------------- | -------- | ------------------------------------------------------------ |
| errcode                                            | int32    | 返回码                                                       |
| errmsg                                             | string   | 错误码描述                                                   |
| datas                                              | obj[]    | 日报数据列表                                                 |
| datas.base_info                                    | obj      | 基础信息                                                     |
| datas.base_info.date                               | uint32   | 日报日期                                                     |
| datas.base_info.record_type                        | uint32   | 记录类型：1-固定上下班；2-外出（此报表中不会出现外出打卡数据）；3-按班次上下班；4-自由签到；5-加班；7-无规则 |
| datas.base_info.name                               | string   | 打卡人员姓名                                                 |
| datas.base_info.name_ex                            | string   | 打卡人员别名                                                 |
| datas.base_info.departs_name                       | string   | 打卡人员所在部门，会显示所有所在部门                         |
| datas.base_info.acctid                             | string   | 打卡人员账号，即userid                                       |
| datas.base_info.rule_info                          | obj      | 打卡人员所属规则信息                                         |
| datas.base_info.rule_info.groupid                  | int32    | 所属规则的id                                                 |
| datas.base_info.rule_info.groupname                | string   | 打卡规则名                                                   |
| datas.base_info.rule_info.scheduleid               | int32    | 当日所属班次id，仅按班次上下班才有值，显示在打卡日报-班次列  |
| datas.base_info.rule_info.schedulename             | string   | 当日所属班次名称，仅按班次上下班才有值，显示在打卡日报-班次列 |
| datas.base_info.rule_info.checkintime              | obj[]    | 当日打卡时间，仅固定上下班规则有值，显示在打卡日报-班次列    |
| datas.base_info.rule_info.checkintime.work_sec     | uint32   | 上班时间，为距离0点的时间差                                  |
| datas.base_info.rule_info.checkintime.off_work_sec | uint32   | 下班时间，为距离0点的时间差                                  |
| datas.base_info.day_type                           | uint32   | 日报类型：0-工作日日报；1-休息日日报                         |
| datas.summary_info                                 | obj      | 汇总信息                                                     |
| datas.summary_info.checkin_count                   | int32    | 当日打卡次数                                                 |
| datas.summary_info.regular_work_sec                | int32    | 当日实际工作时长，单位：秒                                   |
| datas.summary_info.standard_work_sec               | int32    | 当日标准工作时长，单位：秒                                   |
| datas.summary_info.earliest_time                   | int32    | 当日最早打卡时间                                             |
| datas.summary_info.lastest_time                    | int32    | 当日最晚打卡时间                                             |
| datas.holiday_infos                                | obj[]    | 假勤相关信息                                                 |
| datas.holiday_infos.sp_number                      | string   | 假勤申请id，即当日关联的假勤审批单id                         |
| datas.holiday_infos.sp_title                       | obj      | 假勤信息摘要-标题信息                                        |
| datas.holiday_infos.sp_title.data                  | obj[]    | 多种语言描述，目前只有中文一种                               |
| datas.holiday_infos.sp_title.data.text             | string   | 假勤信息摘要-标题文本                                        |
| datas.holiday_infos.sp_title.data.lang             | string   | 语言类型："zh_CN"                                            |
| datas.holiday_infos.sp_description                 | obj      | 假勤信息摘要-描述信息                                        |
| datas.holiday_infos.sp_description.data            | obj[]    | 多种语言描述，目前只有中文一种                               |
| datas.holiday_infos.sp_description.data.text       | string   | 假勤信息摘要-描述文本                                        |
| datas.holiday_infos.sp_description.data.lang       | string   | 语言类型："zh_CN"                                            |
| datas.exception_infos                              | obj[]    | 校准状态信息                                                 |
| datas.exception_infos.exception                    | uint32   | 校准状态类型：1-迟到；2-早退；3-缺卡；4-旷工；5-地点异常；6-设备异常 |
| datas.exception_infos.count                        | int32    | 当日此异常的次数                                             |
| datas.exception_infos.duration                     | int32    | 当日此异常的时长（迟到/早退/旷工才有值）                     |
| datas.ot_info                                      | obj      | 加班信息                                                     |
| datas.ot_info.ot_status                            | uint32   | 状态：0-无加班；1-正常；2-缺时长                             |
| datas.ot_info.ot_duration                          | uint32   | 加班时长                                                     |
| datas.ot_info.exception_duration                   | uint32[] | ot_status为2下，加班不足的时长                               |
| datas.ot_info.workday_over_as_vacation             | int32    | 工作日加班记为调休，单位秒                                   |
| datas.ot_info.workday_over_as_money                | int32    | 工作日加班记为加班费，单位秒                                 |
| datas.ot_info.restday_over_as_vacation             | int32    | 休息日加班记为调休，单位秒                                   |
| datas.ot_info.restday_over_as_money                | int32    | 休息日加班记为加班费，单位秒                                 |
| datas.ot_info.holiday_over_as_vacation             | int32    | 节假日加班记为调休，单位秒                                   |
| datas.ot_info.holiday_over_as_money                | int32    | 节假日加班记为加班费，单位秒                                 |
| datas.sp_items                                     | obj[]    | 假勤统计信息                                                 |
| datas.sp_items.type                                | uint32   | 类型：1-请假；2-补卡；3-出差；4-外出；15-审批打卡；100-外勤  |
| datas.sp_items.vacation_id                         | uint32   | 具体请假类型，当type为1请假时，具体的请假类型id，可通过审批相关接口获取假期详情 |
| datas.sp_items.count                               | uint32   | 当日假勤次数                                                 |
| datas.sp_items.duration                            | uint32   | 当日假勤时长秒数，时长单位为天直接除以86400即为天数，单位为小时直接除以3600即为小时数 |
| datas.sp_items.time_type                           | uint32   | 时长单位：0-按天 1-按小时                                    |
| datas.sp_items.name                                | string   | 统计项名称                                                   |

样例报文：

```
{
    "errcode":0,
    "errmsg":"ok",
    "datas":[
        {
            "base_info":{
                "date":1599062400,
                "record_type":1,
                "name":"张三",
                "name_ex":"Three Zhang",
                "departs_name":"有家企业/realempty;有家企业;有家企业/部门A4",
                "acctid":"ZhangSan",
                "rule_info":{
                    "groupid":10,
                    "groupname":"规则测试",
                    "scheduleid":0,
                    "schedulename":"",
                    "checkintime":[
                        {
                            "work_sec":38760,
                            "off_work_sec":38880
                        }
                    ]
                },
                "day_type":0
            },
            "summary_info":{
                "checkin_count":2,
                "regular_work_sec":31,
                "standard_work_sec":120,
                "earliest_time":38827,
                "lastest_time":38858
            },
            "holiday_infos":[
                {
                    "sp_description":{
                        "data":[
                            {
                                "lang":"zh_CN",
                                "text":"09/03 10:00~09/03 10:01"
                            }
                        ]
                    },
                    "sp_number":"202009030002",
                    "sp_title":{
                        "data":[
                            {
                                "lang":"zh_CN",
                                "text":"请假0.1小时"
                            }
                        ]
                    }
                },
                {
                    "sp_description":{
                        "data":[
                            {
                                "lang":"zh_CN",
                                "text":"08/25 14:37~09/10 14:37"
                            }
                        ]
                    },
                    "sp_number":"202008270004",
                    "sp_title":{
                        "data":[
                            {
                                "lang":"zh_CN",
                                "text":"加班17.0小时"
                            }
                        ]
                    }
                }
            ],
            "exception_infos":[
                {
                    "count":1,
                    "duration":60,
                    "exception":1
                },
                {
                    "count":1,
                    "duration":60,
                    "exception":2
                }
            ],
            "ot_info":{
                "ot_status":1,
                "ot_duration":3600,
                "exception_duration":[],
				"workday_over_as_money": 54000
            },
            "sp_items":[
                {
                    "count":1,
                    "duration":360,
                    "time_type":0,
                    "type":1,
                    "vacation_id":2,
					"name":"年假"
                },
                {
                    "count":0,
                    "duration":0,
                    "time_type":0,
                    "type":100,
                    "vacation_id":0,
					"name":"外勤次数"
                }
            ]
        }
    ]
}
```

