<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.timetrex.system.mapper.VendorMapper">
    
    <resultMap type="Vendor" id="VendorResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="avgWorkDays"    column="avg_work_days"    />
    </resultMap>

    <sql id="selectVendorVo">
        select id, name, avg_work_days from vendor
    </sql>

    <select id="selectVendorList" parameterType="Vendor" resultMap="VendorResult">
        <include refid="selectVendorVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="avgWorkDays != null "> and avg_work_days = #{avgWorkDays}</if>
        </where>
    </select>
    
    <select id="selectVendorById" parameterType="Long" resultMap="VendorResult">
        <include refid="selectVendorVo"/>
        where id = #{id}
    </select>

    <insert id="insertVendor" parameterType="Vendor" useGeneratedKeys="true" keyProperty="id">
        insert into vendor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="avgWorkDays != null">avg_work_days,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="avgWorkDays != null">#{avgWorkDays},</if>
         </trim>
    </insert>

    <update id="updateVendor" parameterType="Vendor">
        update vendor
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="avgWorkDays != null">avg_work_days = #{avgWorkDays},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVendorById" parameterType="Long">
        delete from vendor where id = #{id}
    </delete>

    <delete id="deleteVendorByIds" parameterType="String">
        delete from vendor where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>